{"ast": null, "code": "import { schemaDeserializationMiddleware } from \"./schemaDeserializationMiddleware\";\nimport { schemaSerializationMiddleware } from \"./schemaSerializationMiddleware\";\nexport const deserializerMiddlewareOption = {\n  name: \"deserializerMiddleware\",\n  step: \"deserialize\",\n  tags: [\"DESERIALIZER\"],\n  override: true\n};\nexport const serializerMiddlewareOption = {\n  name: \"serializerMiddleware\",\n  step: \"serialize\",\n  tags: [\"SERIALIZER\"],\n  override: true\n};\nexport function getSchemaSerdePlugin(config) {\n  return {\n    applyToStack: commandStack => {\n      commandStack.add(schemaSerializationMiddleware(config), serializerMiddlewareOption);\n      commandStack.add(schemaDeserializationMiddleware(config), deserializerMiddlewareOption);\n      config.protocol.setSerdeContext(config);\n    }\n  };\n}", "map": {"version": 3, "names": ["schemaDeserializationMiddleware", "schemaSerializationMiddleware", "deserializerMiddlewareOption", "name", "step", "tags", "override", "serializerMiddlewareOption", "getSchemaSerdePlugin", "config", "applyToStack", "commandStack", "add", "protocol", "setSerdeContext"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/core/dist-es/submodules/schema/middleware/getSchemaSerdePlugin.js"], "sourcesContent": ["import { schemaDeserializationMiddleware } from \"./schemaDeserializationMiddleware\";\nimport { schemaSerializationMiddleware } from \"./schemaSerializationMiddleware\";\nexport const deserializerMiddlewareOption = {\n    name: \"deserializerMiddleware\",\n    step: \"deserialize\",\n    tags: [\"DESERIALIZER\"],\n    override: true,\n};\nexport const serializerMiddlewareOption = {\n    name: \"serializerMiddleware\",\n    step: \"serialize\",\n    tags: [\"SERIALIZER\"],\n    override: true,\n};\nexport function getSchemaSerdePlugin(config) {\n    return {\n        applyToStack: (commandStack) => {\n            commandStack.add(schemaSerializationMiddleware(config), serializerMiddlewareOption);\n            commandStack.add(schemaDeserializationMiddleware(config), deserializerMiddlewareOption);\n            config.protocol.setSerdeContext(config);\n        },\n    };\n}\n"], "mappings": "AAAA,SAASA,+BAA+B,QAAQ,mCAAmC;AACnF,SAASC,6BAA6B,QAAQ,iCAAiC;AAC/E,OAAO,MAAMC,4BAA4B,GAAG;EACxCC,IAAI,EAAE,wBAAwB;EAC9BC,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,CAAC,cAAc,CAAC;EACtBC,QAAQ,EAAE;AACd,CAAC;AACD,OAAO,MAAMC,0BAA0B,GAAG;EACtCJ,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,CAAC,YAAY,CAAC;EACpBC,QAAQ,EAAE;AACd,CAAC;AACD,OAAO,SAASE,oBAAoBA,CAACC,MAAM,EAAE;EACzC,OAAO;IACHC,YAAY,EAAGC,YAAY,IAAK;MAC5BA,YAAY,CAACC,GAAG,CAACX,6BAA6B,CAACQ,MAAM,CAAC,EAAEF,0BAA0B,CAAC;MACnFI,YAAY,CAACC,GAAG,CAACZ,+BAA+B,CAACS,MAAM,CAAC,EAAEP,4BAA4B,CAAC;MACvFO,MAAM,CAACI,QAAQ,CAACC,eAAe,CAACL,MAAM,CAAC;IAC3C;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}