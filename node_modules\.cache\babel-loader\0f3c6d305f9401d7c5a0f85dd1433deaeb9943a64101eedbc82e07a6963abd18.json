{"ast": null, "code": "export var HttpAuthLocation;\n(function (HttpAuthLocation) {\n  HttpAuthLocation[\"HEADER\"] = \"header\";\n  HttpAuthLocation[\"QUERY\"] = \"query\";\n})(HttpAuthLocation || (HttpAuthLocation = {}));", "map": {"version": 3, "names": ["HttpAuthLocation"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/types/dist-es/auth/auth.js"], "sourcesContent": ["export var HttpAuthLocation;\n(function (HttpAuthLocation) {\n    HttpAuthLocation[\"HEADER\"] = \"header\";\n    HttpAuthLocation[\"QUERY\"] = \"query\";\n})(HttpAuthLocation || (HttpAuthLocation = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACrCA,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO;AACvC,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}