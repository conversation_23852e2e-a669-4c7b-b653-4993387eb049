{"ast": null, "code": "export function getChunkedStream(source) {\n  let currentMessageTotalLength = 0;\n  let currentMessagePendingLength = 0;\n  let currentMessage = null;\n  let messageLengthBuffer = null;\n  const allocateMessage = size => {\n    if (typeof size !== \"number\") {\n      throw new Error(\"Attempted to allocate an event message where size was not a number: \" + size);\n    }\n    currentMessageTotalLength = size;\n    currentMessagePendingLength = 4;\n    currentMessage = new Uint8Array(size);\n    const currentMessageView = new DataView(currentMessage.buffer);\n    currentMessageView.setUint32(0, size, false);\n  };\n  const iterator = async function* () {\n    const sourceIterator = source[Symbol.asyncIterator]();\n    while (true) {\n      const {\n        value,\n        done\n      } = await sourceIterator.next();\n      if (done) {\n        if (!currentMessageTotalLength) {\n          return;\n        } else if (currentMessageTotalLength === currentMessagePendingLength) {\n          yield currentMessage;\n        } else {\n          throw new Error(\"Truncated event message received.\");\n        }\n        return;\n      }\n      const chunkLength = value.length;\n      let currentOffset = 0;\n      while (currentOffset < chunkLength) {\n        if (!currentMessage) {\n          const bytesRemaining = chunkLength - currentOffset;\n          if (!messageLengthBuffer) {\n            messageLengthBuffer = new Uint8Array(4);\n          }\n          const numBytesForTotal = Math.min(4 - currentMessagePendingLength, bytesRemaining);\n          messageLengthBuffer.set(value.slice(currentOffset, currentOffset + numBytesForTotal), currentMessagePendingLength);\n          currentMessagePendingLength += numBytesForTotal;\n          currentOffset += numBytesForTotal;\n          if (currentMessagePendingLength < 4) {\n            break;\n          }\n          allocateMessage(new DataView(messageLengthBuffer.buffer).getUint32(0, false));\n          messageLengthBuffer = null;\n        }\n        const numBytesToWrite = Math.min(currentMessageTotalLength - currentMessagePendingLength, chunkLength - currentOffset);\n        currentMessage.set(value.slice(currentOffset, currentOffset + numBytesToWrite), currentMessagePendingLength);\n        currentMessagePendingLength += numBytesToWrite;\n        currentOffset += numBytesToWrite;\n        if (currentMessageTotalLength && currentMessageTotalLength === currentMessagePendingLength) {\n          yield currentMessage;\n          currentMessage = null;\n          currentMessageTotalLength = 0;\n          currentMessagePendingLength = 0;\n        }\n      }\n    }\n  };\n  return {\n    [Symbol.asyncIterator]: iterator\n  };\n}", "map": {"version": 3, "names": ["getChunkedStream", "source", "currentMessageTotalLength", "currentMessagePendingLength", "currentMessage", "message<PERSON><PERSON>th<PERSON>uffer", "allocateMessage", "size", "Error", "Uint8Array", "currentMessageView", "DataView", "buffer", "setUint32", "iterator", "sourceIterator", "Symbol", "asyncIterator", "value", "done", "next", "chunkLength", "length", "currentOffset", "bytesRemaining", "numBytesForTotal", "Math", "min", "set", "slice", "getUint32", "numBytesToWrite"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/eventstream-serde-universal/dist-es/getChunkedStream.js"], "sourcesContent": ["export function getChunkedStream(source) {\n    let currentMessageTotalLength = 0;\n    let currentMessagePendingLength = 0;\n    let currentMessage = null;\n    let messageLengthBuffer = null;\n    const allocateMessage = (size) => {\n        if (typeof size !== \"number\") {\n            throw new Error(\"Attempted to allocate an event message where size was not a number: \" + size);\n        }\n        currentMessageTotalLength = size;\n        currentMessagePendingLength = 4;\n        currentMessage = new Uint8Array(size);\n        const currentMessageView = new DataView(currentMessage.buffer);\n        currentMessageView.setUint32(0, size, false);\n    };\n    const iterator = async function* () {\n        const sourceIterator = source[Symbol.asyncIterator]();\n        while (true) {\n            const { value, done } = await sourceIterator.next();\n            if (done) {\n                if (!currentMessageTotalLength) {\n                    return;\n                }\n                else if (currentMessageTotalLength === currentMessagePendingLength) {\n                    yield currentMessage;\n                }\n                else {\n                    throw new Error(\"Truncated event message received.\");\n                }\n                return;\n            }\n            const chunkLength = value.length;\n            let currentOffset = 0;\n            while (currentOffset < chunkLength) {\n                if (!currentMessage) {\n                    const bytesRemaining = chunkLength - currentOffset;\n                    if (!messageLengthBuffer) {\n                        messageLengthBuffer = new Uint8Array(4);\n                    }\n                    const numBytesForTotal = Math.min(4 - currentMessagePendingLength, bytesRemaining);\n                    messageLengthBuffer.set(value.slice(currentOffset, currentOffset + numBytesForTotal), currentMessagePendingLength);\n                    currentMessagePendingLength += numBytesForTotal;\n                    currentOffset += numBytesForTotal;\n                    if (currentMessagePendingLength < 4) {\n                        break;\n                    }\n                    allocateMessage(new DataView(messageLengthBuffer.buffer).getUint32(0, false));\n                    messageLengthBuffer = null;\n                }\n                const numBytesToWrite = Math.min(currentMessageTotalLength - currentMessagePendingLength, chunkLength - currentOffset);\n                currentMessage.set(value.slice(currentOffset, currentOffset + numBytesToWrite), currentMessagePendingLength);\n                currentMessagePendingLength += numBytesToWrite;\n                currentOffset += numBytesToWrite;\n                if (currentMessageTotalLength && currentMessageTotalLength === currentMessagePendingLength) {\n                    yield currentMessage;\n                    currentMessage = null;\n                    currentMessageTotalLength = 0;\n                    currentMessagePendingLength = 0;\n                }\n            }\n        }\n    };\n    return {\n        [Symbol.asyncIterator]: iterator,\n    };\n}\n"], "mappings": "AAAA,OAAO,SAASA,gBAAgBA,CAACC,MAAM,EAAE;EACrC,IAAIC,yBAAyB,GAAG,CAAC;EACjC,IAAIC,2BAA2B,GAAG,CAAC;EACnC,IAAIC,cAAc,GAAG,IAAI;EACzB,IAAIC,mBAAmB,GAAG,IAAI;EAC9B,MAAMC,eAAe,GAAIC,IAAI,IAAK;IAC9B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC1B,MAAM,IAAIC,KAAK,CAAC,sEAAsE,GAAGD,IAAI,CAAC;IAClG;IACAL,yBAAyB,GAAGK,IAAI;IAChCJ,2BAA2B,GAAG,CAAC;IAC/BC,cAAc,GAAG,IAAIK,UAAU,CAACF,IAAI,CAAC;IACrC,MAAMG,kBAAkB,GAAG,IAAIC,QAAQ,CAACP,cAAc,CAACQ,MAAM,CAAC;IAC9DF,kBAAkB,CAACG,SAAS,CAAC,CAAC,EAAEN,IAAI,EAAE,KAAK,CAAC;EAChD,CAAC;EACD,MAAMO,QAAQ,GAAG,gBAAAA,CAAA,EAAmB;IAChC,MAAMC,cAAc,GAAGd,MAAM,CAACe,MAAM,CAACC,aAAa,CAAC,CAAC,CAAC;IACrD,OAAO,IAAI,EAAE;MACT,MAAM;QAAEC,KAAK;QAAEC;MAAK,CAAC,GAAG,MAAMJ,cAAc,CAACK,IAAI,CAAC,CAAC;MACnD,IAAID,IAAI,EAAE;QACN,IAAI,CAACjB,yBAAyB,EAAE;UAC5B;QACJ,CAAC,MACI,IAAIA,yBAAyB,KAAKC,2BAA2B,EAAE;UAChE,MAAMC,cAAc;QACxB,CAAC,MACI;UACD,MAAM,IAAII,KAAK,CAAC,mCAAmC,CAAC;QACxD;QACA;MACJ;MACA,MAAMa,WAAW,GAAGH,KAAK,CAACI,MAAM;MAChC,IAAIC,aAAa,GAAG,CAAC;MACrB,OAAOA,aAAa,GAAGF,WAAW,EAAE;QAChC,IAAI,CAACjB,cAAc,EAAE;UACjB,MAAMoB,cAAc,GAAGH,WAAW,GAAGE,aAAa;UAClD,IAAI,CAAClB,mBAAmB,EAAE;YACtBA,mBAAmB,GAAG,IAAII,UAAU,CAAC,CAAC,CAAC;UAC3C;UACA,MAAMgB,gBAAgB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGxB,2BAA2B,EAAEqB,cAAc,CAAC;UAClFnB,mBAAmB,CAACuB,GAAG,CAACV,KAAK,CAACW,KAAK,CAACN,aAAa,EAAEA,aAAa,GAAGE,gBAAgB,CAAC,EAAEtB,2BAA2B,CAAC;UAClHA,2BAA2B,IAAIsB,gBAAgB;UAC/CF,aAAa,IAAIE,gBAAgB;UACjC,IAAItB,2BAA2B,GAAG,CAAC,EAAE;YACjC;UACJ;UACAG,eAAe,CAAC,IAAIK,QAAQ,CAACN,mBAAmB,CAACO,MAAM,CAAC,CAACkB,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;UAC7EzB,mBAAmB,GAAG,IAAI;QAC9B;QACA,MAAM0B,eAAe,GAAGL,IAAI,CAACC,GAAG,CAACzB,yBAAyB,GAAGC,2BAA2B,EAAEkB,WAAW,GAAGE,aAAa,CAAC;QACtHnB,cAAc,CAACwB,GAAG,CAACV,KAAK,CAACW,KAAK,CAACN,aAAa,EAAEA,aAAa,GAAGQ,eAAe,CAAC,EAAE5B,2BAA2B,CAAC;QAC5GA,2BAA2B,IAAI4B,eAAe;QAC9CR,aAAa,IAAIQ,eAAe;QAChC,IAAI7B,yBAAyB,IAAIA,yBAAyB,KAAKC,2BAA2B,EAAE;UACxF,MAAMC,cAAc;UACpBA,cAAc,GAAG,IAAI;UACrBF,yBAAyB,GAAG,CAAC;UAC7BC,2BAA2B,GAAG,CAAC;QACnC;MACJ;IACJ;EACJ,CAAC;EACD,OAAO;IACH,CAACa,MAAM,CAACC,aAAa,GAAGH;EAC5B,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}