{"ast": null, "code": "export const getResolvedSigningRegion = (hostname, {\n  signingRegion,\n  regionRegex,\n  useFipsEndpoint\n}) => {\n  if (signingRegion) {\n    return signingRegion;\n  } else if (useFipsEndpoint) {\n    const regionRegexJs = regionRegex.replace(\"\\\\\\\\\", \"\\\\\").replace(/^\\^/g, \"\\\\.\").replace(/\\$$/g, \"\\\\.\");\n    const regionRegexmatchArray = hostname.match(regionRegexJs);\n    if (regionRegexmatchArray) {\n      return regionRegexmatchArray[0].slice(1, -1);\n    }\n  }\n};", "map": {"version": 3, "names": ["getResolvedSigningRegion", "hostname", "signingRegion", "regionRegex", "useFipsEndpoint", "regionRegexJs", "replace", "regionRegexmatchArray", "match", "slice"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedSigningRegion.js"], "sourcesContent": ["export const getResolvedSigningRegion = (hostname, { signingRegion, regionRegex, useFipsEndpoint }) => {\n    if (signingRegion) {\n        return signingRegion;\n    }\n    else if (useFipsEndpoint) {\n        const regionRegexJs = regionRegex.replace(\"\\\\\\\\\", \"\\\\\").replace(/^\\^/g, \"\\\\.\").replace(/\\$$/g, \"\\\\.\");\n        const regionRegexmatchArray = hostname.match(regionRegexJs);\n        if (regionRegexmatchArray) {\n            return regionRegexmatchArray[0].slice(1, -1);\n        }\n    }\n};\n"], "mappings": "AAAA,OAAO,MAAMA,wBAAwB,GAAGA,CAACC,QAAQ,EAAE;EAAEC,aAAa;EAAEC,WAAW;EAAEC;AAAgB,CAAC,KAAK;EACnG,IAAIF,aAAa,EAAE;IACf,OAAOA,aAAa;EACxB,CAAC,MACI,IAAIE,eAAe,EAAE;IACtB,MAAMC,aAAa,GAAGF,WAAW,CAACG,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC;IACrG,MAAMC,qBAAqB,GAAGN,QAAQ,CAACO,KAAK,CAACH,aAAa,CAAC;IAC3D,IAAIE,qBAAqB,EAAE;MACvB,OAAOA,qBAAqB,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChD;EACJ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}