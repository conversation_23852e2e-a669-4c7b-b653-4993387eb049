{"ast": null, "code": "var getMapData = require('./_getMapData');\n\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction mapCacheDelete(key) {\n  var result = getMapData(this, key)['delete'](key);\n  this.size -= result ? 1 : 0;\n  return result;\n}\nmodule.exports = mapCacheDelete;", "map": {"version": 3, "names": ["getMapData", "require", "mapCacheDelete", "key", "result", "size", "module", "exports"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/lodash/_mapCacheDelete.js"], "sourcesContent": ["var getMapData = require('./_getMapData');\n\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction mapCacheDelete(key) {\n  var result = getMapData(this, key)['delete'](key);\n  this.size -= result ? 1 : 0;\n  return result;\n}\n\nmodule.exports = mapCacheDelete;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,GAAG,EAAE;EAC3B,IAAIC,MAAM,GAAGJ,UAAU,CAAC,IAAI,EAAEG,GAAG,CAAC,CAAC,QAAQ,CAAC,CAACA,GAAG,CAAC;EACjD,IAAI,CAACE,IAAI,IAAID,MAAM,GAAG,CAAC,GAAG,CAAC;EAC3B,OAAOA,MAAM;AACf;AAEAE,MAAM,CAACC,OAAO,GAAGL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}