{"ast": null, "code": "export default function descending(a, b) {\n  return a == null || b == null ? NaN : b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}", "map": {"version": 3, "names": ["descending", "a", "b", "NaN"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/d3-array/src/descending.js"], "sourcesContent": ["export default function descending(a, b) {\n  return a == null || b == null ? NaN\n    : b < a ? -1\n    : b > a ? 1\n    : b >= a ? 0\n    : NaN;\n}\n"], "mappings": "AAAA,eAAe,SAASA,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACvC,OAAOD,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,GAAGC,GAAG,GAC/BD,CAAC,GAAGD,CAAC,GAAG,CAAC,CAAC,GACVC,CAAC,GAAGD,CAAC,GAAG,CAAC,GACTC,CAAC,IAAID,CAAC,GAAG,CAAC,GACVE,GAAG;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}