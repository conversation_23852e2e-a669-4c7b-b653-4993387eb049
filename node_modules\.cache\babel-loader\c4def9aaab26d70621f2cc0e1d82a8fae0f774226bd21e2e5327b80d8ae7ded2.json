{"ast": null, "code": "import { TypeRegistry } from \"../TypeRegistry\";\nimport { StructureSchema } from \"./StructureSchema\";\nexport class ErrorSchema extends StructureSchema {\n  constructor(name, traits, memberNames, memberList, ctor) {\n    super(name, traits, memberNames, memberList);\n    this.name = name;\n    this.traits = traits;\n    this.memberNames = memberNames;\n    this.memberList = memberList;\n    this.ctor = ctor;\n    this.symbol = ErrorSchema.symbol;\n  }\n  static [Symbol.hasInstance](lhs) {\n    const isPrototype = ErrorSchema.prototype.isPrototypeOf(lhs);\n    if (!isPrototype && typeof lhs === \"object\" && lhs !== null) {\n      const err = lhs;\n      return err.symbol === ErrorSchema.symbol;\n    }\n    return isPrototype;\n  }\n}\nErrorSchema.symbol = Symbol.for(\"@smithy/core/schema::ErrorSchema\");\nexport function error(namespace, name, traits = {}, memberNames, memberList, ctor) {\n  const schema = new ErrorSchema(namespace + \"#\" + name, traits, memberNames, memberList, ctor);\n  TypeRegistry.for(namespace).register(name, schema);\n  return schema;\n}", "map": {"version": 3, "names": ["TypeRegistry", "StructureSchema", "ErrorSchema", "constructor", "name", "traits", "memberNames", "memberList", "ctor", "symbol", "Symbol", "hasInstance", "lhs", "isPrototype", "prototype", "isPrototypeOf", "err", "for", "error", "namespace", "schema", "register"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/core/dist-es/submodules/schema/schemas/ErrorSchema.js"], "sourcesContent": ["import { TypeRegistry } from \"../TypeRegistry\";\nimport { StructureSchema } from \"./StructureSchema\";\nexport class ErrorSchema extends StructureSchema {\n    constructor(name, traits, memberNames, memberList, ctor) {\n        super(name, traits, memberNames, memberList);\n        this.name = name;\n        this.traits = traits;\n        this.memberNames = memberNames;\n        this.memberList = memberList;\n        this.ctor = ctor;\n        this.symbol = ErrorSchema.symbol;\n    }\n    static [Symbol.hasInstance](lhs) {\n        const isPrototype = ErrorSchema.prototype.isPrototypeOf(lhs);\n        if (!isPrototype && typeof lhs === \"object\" && lhs !== null) {\n            const err = lhs;\n            return err.symbol === ErrorSchema.symbol;\n        }\n        return isPrototype;\n    }\n}\nErrorSchema.symbol = Symbol.for(\"@smithy/core/schema::ErrorSchema\");\nexport function error(namespace, name, traits = {}, memberNames, memberList, ctor) {\n    const schema = new ErrorSchema(namespace + \"#\" + name, traits, memberNames, memberList, ctor);\n    TypeRegistry.for(namespace).register(name, schema);\n    return schema;\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,OAAO,MAAMC,WAAW,SAASD,eAAe,CAAC;EAC7CE,WAAWA,CAACC,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAE;IACrD,KAAK,CAACJ,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEC,UAAU,CAAC;IAC5C,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGP,WAAW,CAACO,MAAM;EACpC;EACA,QAAQC,MAAM,CAACC,WAAW,EAAEC,GAAG,EAAE;IAC7B,MAAMC,WAAW,GAAGX,WAAW,CAACY,SAAS,CAACC,aAAa,CAACH,GAAG,CAAC;IAC5D,IAAI,CAACC,WAAW,IAAI,OAAOD,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;MACzD,MAAMI,GAAG,GAAGJ,GAAG;MACf,OAAOI,GAAG,CAACP,MAAM,KAAKP,WAAW,CAACO,MAAM;IAC5C;IACA,OAAOI,WAAW;EACtB;AACJ;AACAX,WAAW,CAACO,MAAM,GAAGC,MAAM,CAACO,GAAG,CAAC,kCAAkC,CAAC;AACnE,OAAO,SAASC,KAAKA,CAACC,SAAS,EAAEf,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAE;EAC/E,MAAMY,MAAM,GAAG,IAAIlB,WAAW,CAACiB,SAAS,GAAG,GAAG,GAAGf,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,CAAC;EAC7FR,YAAY,CAACiB,GAAG,CAACE,SAAS,CAAC,CAACE,QAAQ,CAACjB,IAAI,EAAEgB,MAAM,CAAC;EAClD,OAAOA,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}