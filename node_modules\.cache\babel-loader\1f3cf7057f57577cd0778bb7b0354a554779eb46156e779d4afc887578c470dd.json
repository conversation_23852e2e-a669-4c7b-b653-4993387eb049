{"ast": null, "code": "export const numberSelector = (obj, key, type) => {\n  if (!(key in obj)) return undefined;\n  const numberValue = parseInt(obj[key], 10);\n  if (Number.isNaN(numberValue)) {\n    throw new TypeError(`Cannot load ${type} '${key}'. Expected number, got '${obj[key]}'.`);\n  }\n  return numberValue;\n};", "map": {"version": 3, "names": ["numberSelector", "obj", "key", "type", "undefined", "numberValue", "parseInt", "Number", "isNaN", "TypeError"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/util-config-provider/dist-es/numberSelector.js"], "sourcesContent": ["export const numberSelector = (obj, key, type) => {\n    if (!(key in obj))\n        return undefined;\n    const numberValue = parseInt(obj[key], 10);\n    if (Number.isNaN(numberValue)) {\n        throw new TypeError(`Cannot load ${type} '${key}'. Expected number, got '${obj[key]}'.`);\n    }\n    return numberValue;\n};\n"], "mappings": "AAAA,OAAO,MAAMA,cAAc,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EAC9C,IAAI,EAAED,GAAG,IAAID,GAAG,CAAC,EACb,OAAOG,SAAS;EACpB,MAAMC,WAAW,GAAGC,QAAQ,CAACL,GAAG,CAACC,GAAG,CAAC,EAAE,EAAE,CAAC;EAC1C,IAAIK,MAAM,CAACC,KAAK,CAACH,WAAW,CAAC,EAAE;IAC3B,MAAM,IAAII,SAAS,CAAC,eAAeN,IAAI,KAAKD,GAAG,4BAA4BD,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC;EAC5F;EACA,OAAOG,WAAW;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}