{"ast": null, "code": "import { ProviderError } from \"./ProviderError\";\nexport class CredentialsProviderError extends ProviderError {\n  constructor(message, options = true) {\n    super(message, options);\n    this.name = \"CredentialsProviderError\";\n    Object.setPrototypeOf(this, CredentialsProviderError.prototype);\n  }\n}", "map": {"version": 3, "names": ["Provider<PERSON><PERSON>r", "CredentialsProviderError", "constructor", "message", "options", "name", "Object", "setPrototypeOf", "prototype"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/property-provider/dist-es/CredentialsProviderError.js"], "sourcesContent": ["import { ProviderError } from \"./ProviderError\";\nexport class CredentialsProviderError extends ProviderError {\n    constructor(message, options = true) {\n        super(message, options);\n        this.name = \"CredentialsProviderError\";\n        Object.setPrototypeOf(this, CredentialsProviderError.prototype);\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,OAAO,MAAMC,wBAAwB,SAASD,aAAa,CAAC;EACxDE,WAAWA,CAACC,OAAO,EAAEC,OAAO,GAAG,IAAI,EAAE;IACjC,KAAK,CAACD,OAAO,EAAEC,OAAO,CAAC;IACvB,IAAI,CAACC,IAAI,GAAG,0BAA0B;IACtCC,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEN,wBAAwB,CAACO,SAAS,CAAC;EACnE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}