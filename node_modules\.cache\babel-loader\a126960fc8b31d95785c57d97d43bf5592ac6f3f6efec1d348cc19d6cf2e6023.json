{"ast": null, "code": "import { TypeRegistry } from \"../TypeRegistry\";\nimport { Schema } from \"./Schema\";\nexport class SimpleSchema extends Schema {\n  constructor(name, schemaRef, traits) {\n    super(name, traits);\n    this.name = name;\n    this.schemaRef = schemaRef;\n    this.traits = traits;\n    this.symbol = SimpleSchema.symbol;\n  }\n  static [Symbol.hasInstance](lhs) {\n    const isPrototype = SimpleSchema.prototype.isPrototypeOf(lhs);\n    if (!isPrototype && typeof lhs === \"object\" && lhs !== null) {\n      const sim = lhs;\n      return sim.symbol === SimpleSchema.symbol;\n    }\n    return isPrototype;\n  }\n}\nSimpleSchema.symbol = Symbol.for(\"@smithy/core/schema::SimpleSchema\");\nexport function sim(namespace, name, schemaRef, traits) {\n  const schema = new SimpleSchema(namespace + \"#\" + name, schemaRef, traits);\n  TypeRegistry.for(namespace).register(name, schema);\n  return schema;\n}", "map": {"version": 3, "names": ["TypeRegistry", "<PERSON><PERSON><PERSON>", "SimpleSchema", "constructor", "name", "schemaRef", "traits", "symbol", "Symbol", "hasInstance", "lhs", "isPrototype", "prototype", "isPrototypeOf", "sim", "for", "namespace", "schema", "register"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/core/dist-es/submodules/schema/schemas/SimpleSchema.js"], "sourcesContent": ["import { TypeRegistry } from \"../TypeRegistry\";\nimport { Schema } from \"./Schema\";\nexport class SimpleSchema extends Schema {\n    constructor(name, schemaRef, traits) {\n        super(name, traits);\n        this.name = name;\n        this.schemaRef = schemaRef;\n        this.traits = traits;\n        this.symbol = SimpleSchema.symbol;\n    }\n    static [Symbol.hasInstance](lhs) {\n        const isPrototype = SimpleSchema.prototype.isPrototypeOf(lhs);\n        if (!isPrototype && typeof lhs === \"object\" && lhs !== null) {\n            const sim = lhs;\n            return sim.symbol === SimpleSchema.symbol;\n        }\n        return isPrototype;\n    }\n}\nSimpleSchema.symbol = Symbol.for(\"@smithy/core/schema::SimpleSchema\");\nexport function sim(namespace, name, schemaRef, traits) {\n    const schema = new SimpleSchema(namespace + \"#\" + name, schemaRef, traits);\n    TypeRegistry.for(namespace).register(name, schema);\n    return schema;\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,MAAM,QAAQ,UAAU;AACjC,OAAO,MAAMC,YAAY,SAASD,MAAM,CAAC;EACrCE,WAAWA,CAACC,IAAI,EAAEC,SAAS,EAAEC,MAAM,EAAE;IACjC,KAAK,CAACF,IAAI,EAAEE,MAAM,CAAC;IACnB,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGL,YAAY,CAACK,MAAM;EACrC;EACA,QAAQC,MAAM,CAACC,WAAW,EAAEC,GAAG,EAAE;IAC7B,MAAMC,WAAW,GAAGT,YAAY,CAACU,SAAS,CAACC,aAAa,CAACH,GAAG,CAAC;IAC7D,IAAI,CAACC,WAAW,IAAI,OAAOD,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;MACzD,MAAMI,GAAG,GAAGJ,GAAG;MACf,OAAOI,GAAG,CAACP,MAAM,KAAKL,YAAY,CAACK,MAAM;IAC7C;IACA,OAAOI,WAAW;EACtB;AACJ;AACAT,YAAY,CAACK,MAAM,GAAGC,MAAM,CAACO,GAAG,CAAC,mCAAmC,CAAC;AACrE,OAAO,SAASD,GAAGA,CAACE,SAAS,EAAEZ,IAAI,EAAEC,SAAS,EAAEC,MAAM,EAAE;EACpD,MAAMW,MAAM,GAAG,IAAIf,YAAY,CAACc,SAAS,GAAG,GAAG,GAAGZ,IAAI,EAAEC,SAAS,EAAEC,MAAM,CAAC;EAC1EN,YAAY,CAACe,GAAG,CAACC,SAAS,CAAC,CAACE,QAAQ,CAACd,IAAI,EAAEa,MAAM,CAAC;EAClD,OAAOA,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}