{"ast": null, "code": "import { setCredentialFeature } from \"@aws-sdk/core/client\";\nimport { doesIdentityRequireRefresh, isIdentityExpired, memoizeIdentityProvider, normalizeProvider } from \"@smithy/core\";\nimport { SignatureV4 } from \"@smithy/signature-v4\";\nexport const resolveAwsSdkSigV4Config = config => {\n  let inputCredentials = config.credentials;\n  let isUserSupplied = !!config.credentials;\n  let resolvedCredentials = undefined;\n  Object.defineProperty(config, \"credentials\", {\n    set(credentials) {\n      if (credentials && credentials !== inputCredentials && credentials !== resolvedCredentials) {\n        isUserSupplied = true;\n      }\n      inputCredentials = credentials;\n      const memoizedProvider = normalizeCredentialProvider(config, {\n        credentials: inputCredentials,\n        credentialDefaultProvider: config.credentialDefaultProvider\n      });\n      const boundProvider = bindCallerConfig(config, memoizedProvider);\n      if (isUserSupplied && !boundProvider.attributed) {\n        resolvedCredentials = async options => boundProvider(options).then(creds => setCredentialFeature(creds, \"CREDENTIALS_CODE\", \"e\"));\n        resolvedCredentials.memoized = boundProvider.memoized;\n        resolvedCredentials.configBound = boundProvider.configBound;\n        resolvedCredentials.attributed = true;\n      } else {\n        resolvedCredentials = boundProvider;\n      }\n    },\n    get() {\n      return resolvedCredentials;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  config.credentials = inputCredentials;\n  const {\n    signingEscapePath = true,\n    systemClockOffset = config.systemClockOffset || 0,\n    sha256\n  } = config;\n  let signer;\n  if (config.signer) {\n    signer = normalizeProvider(config.signer);\n  } else if (config.regionInfoProvider) {\n    signer = () => normalizeProvider(config.region)().then(async region => [(await config.regionInfoProvider(region, {\n      useFipsEndpoint: await config.useFipsEndpoint(),\n      useDualstackEndpoint: await config.useDualstackEndpoint()\n    })) || {}, region]).then(([regionInfo, region]) => {\n      const {\n        signingRegion,\n        signingService\n      } = regionInfo;\n      config.signingRegion = config.signingRegion || signingRegion || region;\n      config.signingName = config.signingName || signingService || config.serviceId;\n      const params = {\n        ...config,\n        credentials: config.credentials,\n        region: config.signingRegion,\n        service: config.signingName,\n        sha256,\n        uriEscapePath: signingEscapePath\n      };\n      const SignerCtor = config.signerConstructor || SignatureV4;\n      return new SignerCtor(params);\n    });\n  } else {\n    signer = async authScheme => {\n      authScheme = Object.assign({}, {\n        name: \"sigv4\",\n        signingName: config.signingName || config.defaultSigningName,\n        signingRegion: await normalizeProvider(config.region)(),\n        properties: {}\n      }, authScheme);\n      const signingRegion = authScheme.signingRegion;\n      const signingService = authScheme.signingName;\n      config.signingRegion = config.signingRegion || signingRegion;\n      config.signingName = config.signingName || signingService || config.serviceId;\n      const params = {\n        ...config,\n        credentials: config.credentials,\n        region: config.signingRegion,\n        service: config.signingName,\n        sha256,\n        uriEscapePath: signingEscapePath\n      };\n      const SignerCtor = config.signerConstructor || SignatureV4;\n      return new SignerCtor(params);\n    };\n  }\n  const resolvedConfig = Object.assign(config, {\n    systemClockOffset,\n    signingEscapePath,\n    signer\n  });\n  return resolvedConfig;\n};\nexport const resolveAWSSDKSigV4Config = resolveAwsSdkSigV4Config;\nfunction normalizeCredentialProvider(config, {\n  credentials,\n  credentialDefaultProvider\n}) {\n  let credentialsProvider;\n  if (credentials) {\n    if (!credentials?.memoized) {\n      credentialsProvider = memoizeIdentityProvider(credentials, isIdentityExpired, doesIdentityRequireRefresh);\n    } else {\n      credentialsProvider = credentials;\n    }\n  } else {\n    if (credentialDefaultProvider) {\n      credentialsProvider = normalizeProvider(credentialDefaultProvider(Object.assign({}, config, {\n        parentClientConfig: config\n      })));\n    } else {\n      credentialsProvider = async () => {\n        throw new Error(\"@aws-sdk/core::resolveAwsSdkSigV4Config - `credentials` not provided and no credentialDefaultProvider was configured.\");\n      };\n    }\n  }\n  credentialsProvider.memoized = true;\n  return credentialsProvider;\n}\nfunction bindCallerConfig(config, credentialsProvider) {\n  if (credentialsProvider.configBound) {\n    return credentialsProvider;\n  }\n  const fn = async options => credentialsProvider({\n    ...options,\n    callerClientConfig: config\n  });\n  fn.memoized = credentialsProvider.memoized;\n  fn.configBound = true;\n  return fn;\n}", "map": {"version": 3, "names": ["setCredentialFeature", "doesIdentityRequireRefresh", "isIdentityExpired", "memoizeIdentityProvider", "normalizeProvider", "SignatureV4", "resolveAwsSdkSigV4Config", "config", "inputCredentials", "credentials", "isUserSupplied", "resolvedCredentials", "undefined", "Object", "defineProperty", "set", "memoizedProvider", "normalizeCredentialProvider", "credentialDefaultProvider", "boundProvider", "bindCallerConfig", "attributed", "options", "then", "creds", "memoized", "configBound", "get", "enumerable", "configurable", "signingEscapePath", "systemClockOffset", "sha256", "signer", "regionInfoProvider", "region", "useFipsEndpoint", "useDualstackEndpoint", "regionInfo", "signingRegion", "signingService", "<PERSON><PERSON><PERSON>", "serviceId", "params", "service", "uriEscapePath", "Signer<PERSON>tor", "signerConstructor", "authScheme", "assign", "name", "defaultSigningName", "properties", "resolvedConfig", "resolveAWSSDKSigV4Config", "<PERSON><PERSON><PERSON><PERSON>", "parentClientConfig", "Error", "fn", "callerClientConfig"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/resolveAwsSdkSigV4Config.js"], "sourcesContent": ["import { setCredentialFeature } from \"@aws-sdk/core/client\";\nimport { doesIdentityRequireRefresh, isIdentityExpired, memoizeIdentityProvider, normalizeProvider, } from \"@smithy/core\";\nimport { SignatureV4 } from \"@smithy/signature-v4\";\nexport const resolveAwsSdkSigV4Config = (config) => {\n    let inputCredentials = config.credentials;\n    let isUserSupplied = !!config.credentials;\n    let resolvedCredentials = undefined;\n    Object.defineProperty(config, \"credentials\", {\n        set(credentials) {\n            if (credentials && credentials !== inputCredentials && credentials !== resolvedCredentials) {\n                isUserSupplied = true;\n            }\n            inputCredentials = credentials;\n            const memoizedProvider = normalizeCredentialProvider(config, {\n                credentials: inputCredentials,\n                credentialDefaultProvider: config.credentialDefaultProvider,\n            });\n            const boundProvider = bindCallerConfig(config, memoizedProvider);\n            if (isUserSupplied && !boundProvider.attributed) {\n                resolvedCredentials = async (options) => boundProvider(options).then((creds) => setCredentialFeature(creds, \"CREDENTIALS_CODE\", \"e\"));\n                resolvedCredentials.memoized = boundProvider.memoized;\n                resolvedCredentials.configBound = boundProvider.configBound;\n                resolvedCredentials.attributed = true;\n            }\n            else {\n                resolvedCredentials = boundProvider;\n            }\n        },\n        get() {\n            return resolvedCredentials;\n        },\n        enumerable: true,\n        configurable: true,\n    });\n    config.credentials = inputCredentials;\n    const { signingEscapePath = true, systemClockOffset = config.systemClockOffset || 0, sha256, } = config;\n    let signer;\n    if (config.signer) {\n        signer = normalizeProvider(config.signer);\n    }\n    else if (config.regionInfoProvider) {\n        signer = () => normalizeProvider(config.region)()\n            .then(async (region) => [\n            (await config.regionInfoProvider(region, {\n                useFipsEndpoint: await config.useFipsEndpoint(),\n                useDualstackEndpoint: await config.useDualstackEndpoint(),\n            })) || {},\n            region,\n        ])\n            .then(([regionInfo, region]) => {\n            const { signingRegion, signingService } = regionInfo;\n            config.signingRegion = config.signingRegion || signingRegion || region;\n            config.signingName = config.signingName || signingService || config.serviceId;\n            const params = {\n                ...config,\n                credentials: config.credentials,\n                region: config.signingRegion,\n                service: config.signingName,\n                sha256,\n                uriEscapePath: signingEscapePath,\n            };\n            const SignerCtor = config.signerConstructor || SignatureV4;\n            return new SignerCtor(params);\n        });\n    }\n    else {\n        signer = async (authScheme) => {\n            authScheme = Object.assign({}, {\n                name: \"sigv4\",\n                signingName: config.signingName || config.defaultSigningName,\n                signingRegion: await normalizeProvider(config.region)(),\n                properties: {},\n            }, authScheme);\n            const signingRegion = authScheme.signingRegion;\n            const signingService = authScheme.signingName;\n            config.signingRegion = config.signingRegion || signingRegion;\n            config.signingName = config.signingName || signingService || config.serviceId;\n            const params = {\n                ...config,\n                credentials: config.credentials,\n                region: config.signingRegion,\n                service: config.signingName,\n                sha256,\n                uriEscapePath: signingEscapePath,\n            };\n            const SignerCtor = config.signerConstructor || SignatureV4;\n            return new SignerCtor(params);\n        };\n    }\n    const resolvedConfig = Object.assign(config, {\n        systemClockOffset,\n        signingEscapePath,\n        signer,\n    });\n    return resolvedConfig;\n};\nexport const resolveAWSSDKSigV4Config = resolveAwsSdkSigV4Config;\nfunction normalizeCredentialProvider(config, { credentials, credentialDefaultProvider, }) {\n    let credentialsProvider;\n    if (credentials) {\n        if (!credentials?.memoized) {\n            credentialsProvider = memoizeIdentityProvider(credentials, isIdentityExpired, doesIdentityRequireRefresh);\n        }\n        else {\n            credentialsProvider = credentials;\n        }\n    }\n    else {\n        if (credentialDefaultProvider) {\n            credentialsProvider = normalizeProvider(credentialDefaultProvider(Object.assign({}, config, {\n                parentClientConfig: config,\n            })));\n        }\n        else {\n            credentialsProvider = async () => {\n                throw new Error(\"@aws-sdk/core::resolveAwsSdkSigV4Config - `credentials` not provided and no credentialDefaultProvider was configured.\");\n            };\n        }\n    }\n    credentialsProvider.memoized = true;\n    return credentialsProvider;\n}\nfunction bindCallerConfig(config, credentialsProvider) {\n    if (credentialsProvider.configBound) {\n        return credentialsProvider;\n    }\n    const fn = async (options) => credentialsProvider({ ...options, callerClientConfig: config });\n    fn.memoized = credentialsProvider.memoized;\n    fn.configBound = true;\n    return fn;\n}\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,sBAAsB;AAC3D,SAASC,0BAA0B,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,iBAAiB,QAAS,cAAc;AACzH,SAASC,WAAW,QAAQ,sBAAsB;AAClD,OAAO,MAAMC,wBAAwB,GAAIC,MAAM,IAAK;EAChD,IAAIC,gBAAgB,GAAGD,MAAM,CAACE,WAAW;EACzC,IAAIC,cAAc,GAAG,CAAC,CAACH,MAAM,CAACE,WAAW;EACzC,IAAIE,mBAAmB,GAAGC,SAAS;EACnCC,MAAM,CAACC,cAAc,CAACP,MAAM,EAAE,aAAa,EAAE;IACzCQ,GAAGA,CAACN,WAAW,EAAE;MACb,IAAIA,WAAW,IAAIA,WAAW,KAAKD,gBAAgB,IAAIC,WAAW,KAAKE,mBAAmB,EAAE;QACxFD,cAAc,GAAG,IAAI;MACzB;MACAF,gBAAgB,GAAGC,WAAW;MAC9B,MAAMO,gBAAgB,GAAGC,2BAA2B,CAACV,MAAM,EAAE;QACzDE,WAAW,EAAED,gBAAgB;QAC7BU,yBAAyB,EAAEX,MAAM,CAACW;MACtC,CAAC,CAAC;MACF,MAAMC,aAAa,GAAGC,gBAAgB,CAACb,MAAM,EAAES,gBAAgB,CAAC;MAChE,IAAIN,cAAc,IAAI,CAACS,aAAa,CAACE,UAAU,EAAE;QAC7CV,mBAAmB,GAAG,MAAOW,OAAO,IAAKH,aAAa,CAACG,OAAO,CAAC,CAACC,IAAI,CAAEC,KAAK,IAAKxB,oBAAoB,CAACwB,KAAK,EAAE,kBAAkB,EAAE,GAAG,CAAC,CAAC;QACrIb,mBAAmB,CAACc,QAAQ,GAAGN,aAAa,CAACM,QAAQ;QACrDd,mBAAmB,CAACe,WAAW,GAAGP,aAAa,CAACO,WAAW;QAC3Df,mBAAmB,CAACU,UAAU,GAAG,IAAI;MACzC,CAAC,MACI;QACDV,mBAAmB,GAAGQ,aAAa;MACvC;IACJ,CAAC;IACDQ,GAAGA,CAAA,EAAG;MACF,OAAOhB,mBAAmB;IAC9B,CAAC;IACDiB,UAAU,EAAE,IAAI;IAChBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFtB,MAAM,CAACE,WAAW,GAAGD,gBAAgB;EACrC,MAAM;IAAEsB,iBAAiB,GAAG,IAAI;IAAEC,iBAAiB,GAAGxB,MAAM,CAACwB,iBAAiB,IAAI,CAAC;IAAEC;EAAQ,CAAC,GAAGzB,MAAM;EACvG,IAAI0B,MAAM;EACV,IAAI1B,MAAM,CAAC0B,MAAM,EAAE;IACfA,MAAM,GAAG7B,iBAAiB,CAACG,MAAM,CAAC0B,MAAM,CAAC;EAC7C,CAAC,MACI,IAAI1B,MAAM,CAAC2B,kBAAkB,EAAE;IAChCD,MAAM,GAAGA,CAAA,KAAM7B,iBAAiB,CAACG,MAAM,CAAC4B,MAAM,CAAC,CAAC,CAAC,CAC5CZ,IAAI,CAAC,MAAOY,MAAM,IAAK,CACxB,CAAC,MAAM5B,MAAM,CAAC2B,kBAAkB,CAACC,MAAM,EAAE;MACrCC,eAAe,EAAE,MAAM7B,MAAM,CAAC6B,eAAe,CAAC,CAAC;MAC/CC,oBAAoB,EAAE,MAAM9B,MAAM,CAAC8B,oBAAoB,CAAC;IAC5D,CAAC,CAAC,KAAK,CAAC,CAAC,EACTF,MAAM,CACT,CAAC,CACGZ,IAAI,CAAC,CAAC,CAACe,UAAU,EAAEH,MAAM,CAAC,KAAK;MAChC,MAAM;QAAEI,aAAa;QAAEC;MAAe,CAAC,GAAGF,UAAU;MACpD/B,MAAM,CAACgC,aAAa,GAAGhC,MAAM,CAACgC,aAAa,IAAIA,aAAa,IAAIJ,MAAM;MACtE5B,MAAM,CAACkC,WAAW,GAAGlC,MAAM,CAACkC,WAAW,IAAID,cAAc,IAAIjC,MAAM,CAACmC,SAAS;MAC7E,MAAMC,MAAM,GAAG;QACX,GAAGpC,MAAM;QACTE,WAAW,EAAEF,MAAM,CAACE,WAAW;QAC/B0B,MAAM,EAAE5B,MAAM,CAACgC,aAAa;QAC5BK,OAAO,EAAErC,MAAM,CAACkC,WAAW;QAC3BT,MAAM;QACNa,aAAa,EAAEf;MACnB,CAAC;MACD,MAAMgB,UAAU,GAAGvC,MAAM,CAACwC,iBAAiB,IAAI1C,WAAW;MAC1D,OAAO,IAAIyC,UAAU,CAACH,MAAM,CAAC;IACjC,CAAC,CAAC;EACN,CAAC,MACI;IACDV,MAAM,GAAG,MAAOe,UAAU,IAAK;MAC3BA,UAAU,GAAGnC,MAAM,CAACoC,MAAM,CAAC,CAAC,CAAC,EAAE;QAC3BC,IAAI,EAAE,OAAO;QACbT,WAAW,EAAElC,MAAM,CAACkC,WAAW,IAAIlC,MAAM,CAAC4C,kBAAkB;QAC5DZ,aAAa,EAAE,MAAMnC,iBAAiB,CAACG,MAAM,CAAC4B,MAAM,CAAC,CAAC,CAAC;QACvDiB,UAAU,EAAE,CAAC;MACjB,CAAC,EAAEJ,UAAU,CAAC;MACd,MAAMT,aAAa,GAAGS,UAAU,CAACT,aAAa;MAC9C,MAAMC,cAAc,GAAGQ,UAAU,CAACP,WAAW;MAC7ClC,MAAM,CAACgC,aAAa,GAAGhC,MAAM,CAACgC,aAAa,IAAIA,aAAa;MAC5DhC,MAAM,CAACkC,WAAW,GAAGlC,MAAM,CAACkC,WAAW,IAAID,cAAc,IAAIjC,MAAM,CAACmC,SAAS;MAC7E,MAAMC,MAAM,GAAG;QACX,GAAGpC,MAAM;QACTE,WAAW,EAAEF,MAAM,CAACE,WAAW;QAC/B0B,MAAM,EAAE5B,MAAM,CAACgC,aAAa;QAC5BK,OAAO,EAAErC,MAAM,CAACkC,WAAW;QAC3BT,MAAM;QACNa,aAAa,EAAEf;MACnB,CAAC;MACD,MAAMgB,UAAU,GAAGvC,MAAM,CAACwC,iBAAiB,IAAI1C,WAAW;MAC1D,OAAO,IAAIyC,UAAU,CAACH,MAAM,CAAC;IACjC,CAAC;EACL;EACA,MAAMU,cAAc,GAAGxC,MAAM,CAACoC,MAAM,CAAC1C,MAAM,EAAE;IACzCwB,iBAAiB;IACjBD,iBAAiB;IACjBG;EACJ,CAAC,CAAC;EACF,OAAOoB,cAAc;AACzB,CAAC;AACD,OAAO,MAAMC,wBAAwB,GAAGhD,wBAAwB;AAChE,SAASW,2BAA2BA,CAACV,MAAM,EAAE;EAAEE,WAAW;EAAES;AAA2B,CAAC,EAAE;EACtF,IAAIqC,mBAAmB;EACvB,IAAI9C,WAAW,EAAE;IACb,IAAI,CAACA,WAAW,EAAEgB,QAAQ,EAAE;MACxB8B,mBAAmB,GAAGpD,uBAAuB,CAACM,WAAW,EAAEP,iBAAiB,EAAED,0BAA0B,CAAC;IAC7G,CAAC,MACI;MACDsD,mBAAmB,GAAG9C,WAAW;IACrC;EACJ,CAAC,MACI;IACD,IAAIS,yBAAyB,EAAE;MAC3BqC,mBAAmB,GAAGnD,iBAAiB,CAACc,yBAAyB,CAACL,MAAM,CAACoC,MAAM,CAAC,CAAC,CAAC,EAAE1C,MAAM,EAAE;QACxFiD,kBAAkB,EAAEjD;MACxB,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,MACI;MACDgD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;QAC9B,MAAM,IAAIE,KAAK,CAAC,uHAAuH,CAAC;MAC5I,CAAC;IACL;EACJ;EACAF,mBAAmB,CAAC9B,QAAQ,GAAG,IAAI;EACnC,OAAO8B,mBAAmB;AAC9B;AACA,SAASnC,gBAAgBA,CAACb,MAAM,EAAEgD,mBAAmB,EAAE;EACnD,IAAIA,mBAAmB,CAAC7B,WAAW,EAAE;IACjC,OAAO6B,mBAAmB;EAC9B;EACA,MAAMG,EAAE,GAAG,MAAOpC,OAAO,IAAKiC,mBAAmB,CAAC;IAAE,GAAGjC,OAAO;IAAEqC,kBAAkB,EAAEpD;EAAO,CAAC,CAAC;EAC7FmD,EAAE,CAACjC,QAAQ,GAAG8B,mBAAmB,CAAC9B,QAAQ;EAC1CiC,EAAE,CAAChC,WAAW,GAAG,IAAI;EACrB,OAAOgC,EAAE;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}