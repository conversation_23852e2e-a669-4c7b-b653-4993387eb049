{"ast": null, "code": "'use strict';\n\nimport { validate } from './validator.js';\nimport XMLParser from './xmlparser/XMLParser.js';\nimport XMLBuilder from './xmlbuilder/json2xml.js';\nconst XMLValidator = {\n  validate: validate\n};\nexport { XMLParser, XMLValidator, XMLBuilder };", "map": {"version": 3, "names": ["validate", "XMLParser", "XMLBuilder", "XMLValidator"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/fast-xml-parser/src/fxp.js"], "sourcesContent": ["'use strict';\n\nimport {validate} from './validator.js';\nimport XMLParser from './xmlparser/XMLParser.js';\nimport XMLBuilder from './xmlbuilder/json2xml.js';\n\nconst XMLValidator = {\n  validate: validate\n}\nexport {\n  XMLParser,\n  XMLValidator,\n  XMLBuilder\n};"], "mappings": "AAAA,YAAY;;AAEZ,SAAQA,QAAQ,QAAO,gBAAgB;AACvC,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,UAAU,MAAM,0BAA0B;AAEjD,MAAMC,YAAY,GAAG;EACnBH,QAAQ,EAAEA;AACZ,CAAC;AACD,SACEC,SAAS,EACTE,YAAY,EACZD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}