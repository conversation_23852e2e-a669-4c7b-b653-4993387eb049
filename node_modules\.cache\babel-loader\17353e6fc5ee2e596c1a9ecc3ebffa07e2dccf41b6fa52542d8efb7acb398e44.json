{"ast": null, "code": "export class EndpointError extends Error {\n  constructor(message) {\n    super(message);\n    this.name = \"EndpointError\";\n  }\n}", "map": {"version": 3, "names": ["EndpointError", "Error", "constructor", "message", "name"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/util-endpoints/dist-es/types/EndpointError.js"], "sourcesContent": ["export class EndpointError extends Error {\n    constructor(message) {\n        super(message);\n        this.name = \"EndpointError\";\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,aAAa,SAASC,KAAK,CAAC;EACrCC,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,eAAe;EAC/B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}