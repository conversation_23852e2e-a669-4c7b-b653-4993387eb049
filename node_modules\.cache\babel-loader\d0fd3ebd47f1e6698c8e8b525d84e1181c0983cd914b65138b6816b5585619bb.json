{"ast": null, "code": "export { EndpointError } from \"@smithy/util-endpoints\";", "map": {"version": 3, "names": ["EndpointError"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@aws-sdk/util-endpoints/dist-es/types/EndpointError.js"], "sourcesContent": ["export { EndpointError } from \"@smithy/util-endpoints\";\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}