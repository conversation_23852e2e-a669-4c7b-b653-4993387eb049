{"ast": null, "code": "export * from \"./SignatureV4\";\nexport * from \"./constants\";\nexport { getCanonicalHeaders } from \"./getCanonicalHeaders\";\nexport { getCanonicalQuery } from \"./getCanonicalQuery\";\nexport { getPayloadHash } from \"./getPayloadHash\";\nexport { moveHeadersToQuery } from \"./moveHeadersToQuery\";\nexport { prepareRequest } from \"./prepareRequest\";\nexport * from \"./credentialDerivation\";\nexport { SignatureV4Base } from \"./SignatureV4Base\";\nexport { hasHeader } from \"./headerUtil\";\nexport * from \"./signature-v4a-container\";", "map": {"version": 3, "names": ["getCanonicalHeaders", "getCanonicalQuery", "getPayloadHash", "moveHeadersToQuery", "prepareRequest", "SignatureV4Base", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/signature-v4/dist-es/index.js"], "sourcesContent": ["export * from \"./SignatureV4\";\nexport * from \"./constants\";\nexport { getCanonicalHeaders } from \"./getCanonicalHeaders\";\nexport { getCanonicalQuery } from \"./getCanonicalQuery\";\nexport { getPayloadHash } from \"./getPayloadHash\";\nexport { moveHeadersToQuery } from \"./moveHeadersToQuery\";\nexport { prepareRequest } from \"./prepareRequest\";\nexport * from \"./credentialDerivation\";\nexport { SignatureV4Base } from \"./SignatureV4Base\";\nexport { hasHeader } from \"./headerUtil\";\nexport * from \"./signature-v4a-container\";\n"], "mappings": "AAAA,cAAc,eAAe;AAC7B,cAAc,aAAa;AAC3B,SAASA,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,cAAc,wBAAwB;AACtC,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,SAAS,QAAQ,cAAc;AACxC,cAAc,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}