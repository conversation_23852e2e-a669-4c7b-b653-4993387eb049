{"ast": null, "code": "import { TypeRegistry } from \"../TypeRegistry\";\nimport { Schema } from \"./Schema\";\nexport class ListSchema extends Schema {\n  constructor(name, traits, valueSchema) {\n    super(name, traits);\n    this.name = name;\n    this.traits = traits;\n    this.valueSchema = valueSchema;\n    this.symbol = ListSchema.symbol;\n  }\n  static [Symbol.hasInstance](lhs) {\n    const isPrototype = ListSchema.prototype.isPrototypeOf(lhs);\n    if (!isPrototype && typeof lhs === \"object\" && lhs !== null) {\n      const list = lhs;\n      return list.symbol === ListSchema.symbol;\n    }\n    return isPrototype;\n  }\n}\nListSchema.symbol = Symbol.for(\"@smithy/core/schema::ListSchema\");\nexport function list(namespace, name, traits = {}, valueSchema) {\n  const schema = new ListSchema(namespace + \"#\" + name, traits, typeof valueSchema === \"function\" ? valueSchema() : valueSchema);\n  TypeRegistry.for(namespace).register(name, schema);\n  return schema;\n}", "map": {"version": 3, "names": ["TypeRegistry", "<PERSON><PERSON><PERSON>", "ListSchema", "constructor", "name", "traits", "valueSchema", "symbol", "Symbol", "hasInstance", "lhs", "isPrototype", "prototype", "isPrototypeOf", "list", "for", "namespace", "schema", "register"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/core/dist-es/submodules/schema/schemas/ListSchema.js"], "sourcesContent": ["import { TypeRegistry } from \"../TypeRegistry\";\nimport { Schema } from \"./Schema\";\nexport class ListSchema extends Schema {\n    constructor(name, traits, valueSchema) {\n        super(name, traits);\n        this.name = name;\n        this.traits = traits;\n        this.valueSchema = valueSchema;\n        this.symbol = ListSchema.symbol;\n    }\n    static [Symbol.hasInstance](lhs) {\n        const isPrototype = ListSchema.prototype.isPrototypeOf(lhs);\n        if (!isPrototype && typeof lhs === \"object\" && lhs !== null) {\n            const list = lhs;\n            return list.symbol === ListSchema.symbol;\n        }\n        return isPrototype;\n    }\n}\nListSchema.symbol = Symbol.for(\"@smithy/core/schema::ListSchema\");\nexport function list(namespace, name, traits = {}, valueSchema) {\n    const schema = new ListSchema(namespace + \"#\" + name, traits, typeof valueSchema === \"function\" ? valueSchema() : valueSchema);\n    TypeRegistry.for(namespace).register(name, schema);\n    return schema;\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,MAAM,QAAQ,UAAU;AACjC,OAAO,MAAMC,UAAU,SAASD,MAAM,CAAC;EACnCE,WAAWA,CAACC,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAE;IACnC,KAAK,CAACF,IAAI,EAAEC,MAAM,CAAC;IACnB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,MAAM,GAAGL,UAAU,CAACK,MAAM;EACnC;EACA,QAAQC,MAAM,CAACC,WAAW,EAAEC,GAAG,EAAE;IAC7B,MAAMC,WAAW,GAAGT,UAAU,CAACU,SAAS,CAACC,aAAa,CAACH,GAAG,CAAC;IAC3D,IAAI,CAACC,WAAW,IAAI,OAAOD,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;MACzD,MAAMI,IAAI,GAAGJ,GAAG;MAChB,OAAOI,IAAI,CAACP,MAAM,KAAKL,UAAU,CAACK,MAAM;IAC5C;IACA,OAAOI,WAAW;EACtB;AACJ;AACAT,UAAU,CAACK,MAAM,GAAGC,MAAM,CAACO,GAAG,CAAC,iCAAiC,CAAC;AACjE,OAAO,SAASD,IAAIA,CAACE,SAAS,EAAEZ,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAEC,WAAW,EAAE;EAC5D,MAAMW,MAAM,GAAG,IAAIf,UAAU,CAACc,SAAS,GAAG,GAAG,GAAGZ,IAAI,EAAEC,MAAM,EAAE,OAAOC,WAAW,KAAK,UAAU,GAAGA,WAAW,CAAC,CAAC,GAAGA,WAAW,CAAC;EAC9HN,YAAY,CAACe,GAAG,CAACC,SAAS,CAAC,CAACE,QAAQ,CAACd,IAAI,EAAEa,MAAM,CAAC;EAClD,OAAOA,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}