{"ast": null, "code": "const fallbackWindow = {};\nexport function locateWindow() {\n  if (typeof window !== \"undefined\") {\n    return window;\n  } else if (typeof self !== \"undefined\") {\n    return self;\n  }\n  return fallbackWindow;\n}", "map": {"version": 3, "names": ["fallback<PERSON><PERSON><PERSON>", "locateWindow", "window", "self"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@aws-sdk/util-locate-window/dist-es/index.js"], "sourcesContent": ["const fallbackWindow = {};\nexport function locateWindow() {\n    if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else if (typeof self !== \"undefined\") {\n        return self;\n    }\n    return fallbackWindow;\n}\n"], "mappings": "AAAA,MAAMA,cAAc,GAAG,CAAC,CAAC;AACzB,OAAO,SAASC,YAAYA,CAAA,EAAG;EAC3B,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAOA,MAAM;EACjB,CAAC,MACI,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAClC,OAAOA,IAAI;EACf;EACA,OAAOH,cAAc;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}