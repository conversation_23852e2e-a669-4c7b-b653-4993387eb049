{"ast": null, "code": "export const isReadableStream = stream => typeof ReadableStream === \"function\" && (stream?.constructor?.name === ReadableStream.name || stream instanceof ReadableStream);\nexport const isBlob = blob => {\n  return typeof Blob === \"function\" && (blob?.constructor?.name === Blob.name || blob instanceof Blob);\n};", "map": {"version": 3, "names": ["isReadableStream", "stream", "ReadableStream", "constructor", "name", "isBlob", "blob", "Blob"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/util-stream/dist-es/stream-type-check.js"], "sourcesContent": ["export const isReadableStream = (stream) => typeof ReadableStream === \"function\" &&\n    (stream?.constructor?.name === ReadableStream.name || stream instanceof ReadableStream);\nexport const isBlob = (blob) => {\n    return typeof Blob === \"function\" && (blob?.constructor?.name === Blob.name || blob instanceof Blob);\n};\n"], "mappings": "AAAA,OAAO,MAAMA,gBAAgB,GAAIC,MAAM,IAAK,OAAOC,cAAc,KAAK,UAAU,KAC3ED,MAAM,EAAEE,WAAW,EAAEC,IAAI,KAAKF,cAAc,CAACE,IAAI,IAAIH,MAAM,YAAYC,cAAc,CAAC;AAC3F,OAAO,MAAMG,MAAM,GAAIC,IAAI,IAAK;EAC5B,OAAO,OAAOC,IAAI,KAAK,UAAU,KAAKD,IAAI,EAAEH,WAAW,EAAEC,IAAI,KAAKG,IAAI,CAACH,IAAI,IAAIE,IAAI,YAAYC,IAAI,CAAC;AACxG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}