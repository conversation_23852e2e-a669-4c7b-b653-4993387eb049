{"ast": null, "code": "import { getSmithyContext } from \"@smithy/util-middleware\";\nexport const schemaSerializationMiddleware = config => (next, context) => async args => {\n  const {\n    operationSchema\n  } = getSmithyContext(context);\n  const endpoint = context.endpointV2?.url && config.urlParser ? async () => config.urlParser(context.endpointV2.url) : config.endpoint;\n  const request = await config.protocol.serializeRequest(operationSchema, args.input, {\n    ...config,\n    ...context,\n    endpoint\n  });\n  return next({\n    ...args,\n    request\n  });\n};", "map": {"version": 3, "names": ["getSmithyContext", "schemaSerializationMiddleware", "config", "next", "context", "args", "operationSchema", "endpoint", "endpointV2", "url", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "request", "protocol", "serializeRequest", "input"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/core/dist-es/submodules/schema/middleware/schemaSerializationMiddleware.js"], "sourcesContent": ["import { getSmithyContext } from \"@smithy/util-middleware\";\nexport const schemaSerializationMiddleware = (config) => (next, context) => async (args) => {\n    const { operationSchema } = getSmithyContext(context);\n    const endpoint = context.endpointV2?.url && config.urlParser\n        ? async () => config.urlParser(context.endpointV2.url)\n        : config.endpoint;\n    const request = await config.protocol.serializeRequest(operationSchema, args.input, {\n        ...config,\n        ...context,\n        endpoint,\n    });\n    return next({\n        ...args,\n        request,\n    });\n};\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,yBAAyB;AAC1D,OAAO,MAAMC,6BAA6B,GAAIC,MAAM,IAAK,CAACC,IAAI,EAAEC,OAAO,KAAK,MAAOC,IAAI,IAAK;EACxF,MAAM;IAAEC;EAAgB,CAAC,GAAGN,gBAAgB,CAACI,OAAO,CAAC;EACrD,MAAMG,QAAQ,GAAGH,OAAO,CAACI,UAAU,EAAEC,GAAG,IAAIP,MAAM,CAACQ,SAAS,GACtD,YAAYR,MAAM,CAACQ,SAAS,CAACN,OAAO,CAACI,UAAU,CAACC,GAAG,CAAC,GACpDP,MAAM,CAACK,QAAQ;EACrB,MAAMI,OAAO,GAAG,MAAMT,MAAM,CAACU,QAAQ,CAACC,gBAAgB,CAACP,eAAe,EAAED,IAAI,CAACS,KAAK,EAAE;IAChF,GAAGZ,MAAM;IACT,GAAGE,OAAO;IACVG;EACJ,CAAC,CAAC;EACF,OAAOJ,IAAI,CAAC;IACR,GAAGE,IAAI;IACPM;EACJ,CAAC,CAAC;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}