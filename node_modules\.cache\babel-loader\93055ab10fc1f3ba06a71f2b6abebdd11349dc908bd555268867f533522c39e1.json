{"ast": null, "code": "export async function headStream(stream, bytes) {\n  let byteLengthCounter = 0;\n  const chunks = [];\n  const reader = stream.getReader();\n  let isDone = false;\n  while (!isDone) {\n    const {\n      done,\n      value\n    } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      byteLengthCounter += value?.byteLength ?? 0;\n    }\n    if (byteLengthCounter >= bytes) {\n      break;\n    }\n    isDone = done;\n  }\n  reader.releaseLock();\n  const collected = new Uint8Array(Math.min(bytes, byteLengthCounter));\n  let offset = 0;\n  for (const chunk of chunks) {\n    if (chunk.byteLength > collected.byteLength - offset) {\n      collected.set(chunk.subarray(0, collected.byteLength - offset), offset);\n      break;\n    } else {\n      collected.set(chunk, offset);\n    }\n    offset += chunk.length;\n  }\n  return collected;\n}", "map": {"version": 3, "names": ["headStream", "stream", "bytes", "byteLength<PERSON>ounter", "chunks", "reader", "<PERSON><PERSON><PERSON><PERSON>", "isDone", "done", "value", "read", "push", "byteLength", "releaseLock", "collected", "Uint8Array", "Math", "min", "offset", "chunk", "set", "subarray", "length"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/util-stream/dist-es/headStream.browser.js"], "sourcesContent": ["export async function headStream(stream, bytes) {\n    let byteLengthCounter = 0;\n    const chunks = [];\n    const reader = stream.getReader();\n    let isDone = false;\n    while (!isDone) {\n        const { done, value } = await reader.read();\n        if (value) {\n            chunks.push(value);\n            byteLengthCounter += value?.byteLength ?? 0;\n        }\n        if (byteLengthCounter >= bytes) {\n            break;\n        }\n        isDone = done;\n    }\n    reader.releaseLock();\n    const collected = new Uint8Array(Math.min(bytes, byteLengthCounter));\n    let offset = 0;\n    for (const chunk of chunks) {\n        if (chunk.byteLength > collected.byteLength - offset) {\n            collected.set(chunk.subarray(0, collected.byteLength - offset), offset);\n            break;\n        }\n        else {\n            collected.set(chunk, offset);\n        }\n        offset += chunk.length;\n    }\n    return collected;\n}\n"], "mappings": "AAAA,OAAO,eAAeA,UAAUA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC5C,IAAIC,iBAAiB,GAAG,CAAC;EACzB,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMC,MAAM,GAAGJ,MAAM,CAACK,SAAS,CAAC,CAAC;EACjC,IAAIC,MAAM,GAAG,KAAK;EAClB,OAAO,CAACA,MAAM,EAAE;IACZ,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMJ,MAAM,CAACK,IAAI,CAAC,CAAC;IAC3C,IAAID,KAAK,EAAE;MACPL,MAAM,CAACO,IAAI,CAACF,KAAK,CAAC;MAClBN,iBAAiB,IAAIM,KAAK,EAAEG,UAAU,IAAI,CAAC;IAC/C;IACA,IAAIT,iBAAiB,IAAID,KAAK,EAAE;MAC5B;IACJ;IACAK,MAAM,GAAGC,IAAI;EACjB;EACAH,MAAM,CAACQ,WAAW,CAAC,CAAC;EACpB,MAAMC,SAAS,GAAG,IAAIC,UAAU,CAACC,IAAI,CAACC,GAAG,CAACf,KAAK,EAAEC,iBAAiB,CAAC,CAAC;EACpE,IAAIe,MAAM,GAAG,CAAC;EACd,KAAK,MAAMC,KAAK,IAAIf,MAAM,EAAE;IACxB,IAAIe,KAAK,CAACP,UAAU,GAAGE,SAAS,CAACF,UAAU,GAAGM,MAAM,EAAE;MAClDJ,SAAS,CAACM,GAAG,CAACD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAEP,SAAS,CAACF,UAAU,GAAGM,MAAM,CAAC,EAAEA,MAAM,CAAC;MACvE;IACJ,CAAC,MACI;MACDJ,SAAS,CAACM,GAAG,CAACD,KAAK,EAAED,MAAM,CAAC;IAChC;IACAA,MAAM,IAAIC,KAAK,CAACG,MAAM;EAC1B;EACA,OAAOR,SAAS;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}