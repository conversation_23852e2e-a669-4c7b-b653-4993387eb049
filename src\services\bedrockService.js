import { BedrockAgentRuntimeClient, InvokeAgentCommand } from '@aws-sdk/client-bedrock-agent-runtime';

class BedrockService {
  constructor() {
    this.client = null;
    this.initializeClient();
    this.sessionId = this.generateSessionId();
  }

  initializeClient() {
    try {
      // Initialize AWS Bedrock Agent Runtime client
      this.client = new BedrockAgentRuntimeClient({
        region: process.env.REACT_APP_AWS_REGION || 'us-east-1',
        credentials: {
          accessKeyId: process.env.REACT_APP_AWS_ACCESS_KEY_ID || 'your-access-key',
          secretAccessKey: process.env.REACT_APP_AWS_SECRET_ACCESS_KEY || 'your-secret-key',
        
        }
      });
    } catch (error) {
      console.error('Failed to initialize Bedrock client:', error);
    }
  }

  async sendMessage(message, onChunk = null) {
    if (!this.client) {
      throw new Error('Bedrock client not initialized');
    }

    const agentId = process.env.REACT_APP_BEDROCK_AGENT_ID;
    const agentAliasId = process.env.REACT_APP_BEDROCK_AGENT_ALIAS_ID;

    if (!agentId || !agentAliasId) {
      throw new Error('Agent ID and Agent Alias ID must be configured in environment variables');
    }

    try {
      const command = new InvokeAgentCommand({
        agentId: agentId,
        agentAliasId: agentAliasId,
        sessionId: this.sessionId,
        inputText: message,
      });

      const response = await this.client.send(command);
      
      // Handle streaming response
      let fullResponse = '';
      let chartData = null;
      
      if (response.completion) {
        for await (const chunk of response.completion) {
          if (chunk.chunk && chunk.chunk.bytes) {
            const chunkText = new TextDecoder().decode(chunk.chunk.bytes);
            fullResponse += chunkText;
            
            // Call onChunk callback if provided for real-time updates
            if (onChunk) {
              onChunk(chunkText);
            }
          }
        }
      }

      // Try to parse response for chart data
      try {
        const parsedResponse = JSON.parse(fullResponse);
        if (parsedResponse.type === 'chart') {
          chartData = parsedResponse;
          fullResponse = parsedResponse.text || '';
        }
      } catch (parseError) {
        // Response is not JSON, treat as regular text
        // Check if response contains chart data markers
        const chartMatch = fullResponse.match(/\{[\s\S]*"type":\s*"chart"[\s\S]*\}/);
        if (chartMatch) {
          try {
            chartData = JSON.parse(chartMatch[0]);
            fullResponse = fullResponse.replace(chartMatch[0], '').trim();
          } catch (e) {
            // Failed to parse chart data, keep as text
          }
        }
      }

      return {
        text: fullResponse,
        chartData: chartData,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Error calling Bedrock agent:', error);
      throw new Error(`Failed to get response from Bedrock agent: ${error.message}`);
    }
  }

  generateSessionId() {
    // Generate a unique session ID for the conversation
    return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Mock method for development/testing
  async mockSendMessage(message) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock responses based on message content
    if (message.toLowerCase().includes('chart') || message.toLowerCase().includes('pie')) {
      return {
        text: "Here's the supplier performance data you requested:",
        chartData: {
          type: "chart",
          chartType: "pie",
          title: "Supplier Performance Scores",
          data: [
            { label: "Supplier A", value: 85 },
            { label: "Supplier B", value: 92 },
            { label: "Supplier C", value: 78 },
            { label: "Supplier D", value: 88 },
            { label: "Supplier E", value: 95 }
          ]
        },
        timestamp: new Date().toISOString()
      };
    } else if (message.toLowerCase().includes('bar')) {
      return {
        text: "Monthly delivery performance:",
        chartData: {
          type: "chart",
          chartType: "bar",
          title: "Monthly Deliveries",
          data: [
            { label: "Jan", value: 120 },
            { label: "Feb", value: 135 },
            { label: "Mar", value: 98 },
            { label: "Apr", value: 156 },
            { label: "May", value: 142 }
          ]
        },
        timestamp: new Date().toISOString()
      };
    } else {
      return {
        text: `I understand you're asking about: "${message}". I'm here to help with supplier-related queries. You can ask me to create charts, analyze supplier performance, or get delivery information.`,
        chartData: null,
        timestamp: new Date().toISOString()
      };
    }
  }
}

export default new BedrockService();
