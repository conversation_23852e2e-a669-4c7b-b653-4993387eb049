{"ast": null, "code": "import { escapeAttribute } from \"./escape-attribute\";\nimport { XmlText } from \"./XmlText\";\nexport class XmlNode {\n  name;\n  children;\n  attributes = {};\n  static of(name, childText, withName) {\n    const node = new XmlNode(name);\n    if (childText !== undefined) {\n      node.addChildNode(new XmlText(childText));\n    }\n    if (withName !== undefined) {\n      node.withName(withName);\n    }\n    return node;\n  }\n  constructor(name, children = []) {\n    this.name = name;\n    this.children = children;\n  }\n  withName(name) {\n    this.name = name;\n    return this;\n  }\n  addAttribute(name, value) {\n    this.attributes[name] = value;\n    return this;\n  }\n  addChildNode(child) {\n    this.children.push(child);\n    return this;\n  }\n  removeAttribute(name) {\n    delete this.attributes[name];\n    return this;\n  }\n  n(name) {\n    this.name = name;\n    return this;\n  }\n  c(child) {\n    this.children.push(child);\n    return this;\n  }\n  a(name, value) {\n    if (value != null) {\n      this.attributes[name] = value;\n    }\n    return this;\n  }\n  cc(input, field, withName = field) {\n    if (input[field] != null) {\n      const node = XmlNode.of(field, input[field]).withName(withName);\n      this.c(node);\n    }\n  }\n  l(input, listName, memberName, valueProvider) {\n    if (input[listName] != null) {\n      const nodes = valueProvider();\n      nodes.map(node => {\n        node.withName(memberName);\n        this.c(node);\n      });\n    }\n  }\n  lc(input, listName, memberName, valueProvider) {\n    if (input[listName] != null) {\n      const nodes = valueProvider();\n      const containerNode = new XmlNode(memberName);\n      nodes.map(node => {\n        containerNode.c(node);\n      });\n      this.c(containerNode);\n    }\n  }\n  toString() {\n    const hasChildren = Boolean(this.children.length);\n    let xmlText = `<${this.name}`;\n    const attributes = this.attributes;\n    for (const attributeName of Object.keys(attributes)) {\n      const attribute = attributes[attributeName];\n      if (attribute != null) {\n        xmlText += ` ${attributeName}=\"${escapeAttribute(\"\" + attribute)}\"`;\n      }\n    }\n    return xmlText += !hasChildren ? \"/>\" : `>${this.children.map(c => c.toString()).join(\"\")}</${this.name}>`;\n  }\n}", "map": {"version": 3, "names": ["escapeAttribute", "XmlText", "XmlNode", "name", "children", "attributes", "of", "childText", "with<PERSON><PERSON>", "node", "undefined", "addChildNode", "constructor", "addAttribute", "value", "child", "push", "removeAttribute", "n", "c", "a", "cc", "input", "field", "l", "listName", "memberName", "valueProvider", "nodes", "map", "lc", "containerNode", "toString", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Boolean", "length", "xmlText", "attributeName", "Object", "keys", "attribute", "join"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@aws-sdk/xml-builder/dist-es/XmlNode.js"], "sourcesContent": ["import { escapeAttribute } from \"./escape-attribute\";\nimport { XmlText } from \"./XmlText\";\nexport class XmlNode {\n    name;\n    children;\n    attributes = {};\n    static of(name, childText, withName) {\n        const node = new XmlNode(name);\n        if (childText !== undefined) {\n            node.addChildNode(new XmlText(childText));\n        }\n        if (withName !== undefined) {\n            node.withName(withName);\n        }\n        return node;\n    }\n    constructor(name, children = []) {\n        this.name = name;\n        this.children = children;\n    }\n    withName(name) {\n        this.name = name;\n        return this;\n    }\n    addAttribute(name, value) {\n        this.attributes[name] = value;\n        return this;\n    }\n    addChildNode(child) {\n        this.children.push(child);\n        return this;\n    }\n    removeAttribute(name) {\n        delete this.attributes[name];\n        return this;\n    }\n    n(name) {\n        this.name = name;\n        return this;\n    }\n    c(child) {\n        this.children.push(child);\n        return this;\n    }\n    a(name, value) {\n        if (value != null) {\n            this.attributes[name] = value;\n        }\n        return this;\n    }\n    cc(input, field, withName = field) {\n        if (input[field] != null) {\n            const node = XmlNode.of(field, input[field]).withName(withName);\n            this.c(node);\n        }\n    }\n    l(input, listName, memberName, valueProvider) {\n        if (input[listName] != null) {\n            const nodes = valueProvider();\n            nodes.map((node) => {\n                node.withName(memberName);\n                this.c(node);\n            });\n        }\n    }\n    lc(input, listName, memberName, valueProvider) {\n        if (input[listName] != null) {\n            const nodes = valueProvider();\n            const containerNode = new XmlNode(memberName);\n            nodes.map((node) => {\n                containerNode.c(node);\n            });\n            this.c(containerNode);\n        }\n    }\n    toString() {\n        const hasChildren = Boolean(this.children.length);\n        let xmlText = `<${this.name}`;\n        const attributes = this.attributes;\n        for (const attributeName of Object.keys(attributes)) {\n            const attribute = attributes[attributeName];\n            if (attribute != null) {\n                xmlText += ` ${attributeName}=\"${escapeAttribute(\"\" + attribute)}\"`;\n            }\n        }\n        return (xmlText += !hasChildren ? \"/>\" : `>${this.children.map((c) => c.toString()).join(\"\")}</${this.name}>`);\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB;AACpD,SAASC,OAAO,QAAQ,WAAW;AACnC,OAAO,MAAMC,OAAO,CAAC;EACjBC,IAAI;EACJC,QAAQ;EACRC,UAAU,GAAG,CAAC,CAAC;EACf,OAAOC,EAAEA,CAACH,IAAI,EAAEI,SAAS,EAAEC,QAAQ,EAAE;IACjC,MAAMC,IAAI,GAAG,IAAIP,OAAO,CAACC,IAAI,CAAC;IAC9B,IAAII,SAAS,KAAKG,SAAS,EAAE;MACzBD,IAAI,CAACE,YAAY,CAAC,IAAIV,OAAO,CAACM,SAAS,CAAC,CAAC;IAC7C;IACA,IAAIC,QAAQ,KAAKE,SAAS,EAAE;MACxBD,IAAI,CAACD,QAAQ,CAACA,QAAQ,CAAC;IAC3B;IACA,OAAOC,IAAI;EACf;EACAG,WAAWA,CAACT,IAAI,EAAEC,QAAQ,GAAG,EAAE,EAAE;IAC7B,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;EACAI,QAAQA,CAACL,IAAI,EAAE;IACX,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,OAAO,IAAI;EACf;EACAU,YAAYA,CAACV,IAAI,EAAEW,KAAK,EAAE;IACtB,IAAI,CAACT,UAAU,CAACF,IAAI,CAAC,GAAGW,KAAK;IAC7B,OAAO,IAAI;EACf;EACAH,YAAYA,CAACI,KAAK,EAAE;IAChB,IAAI,CAACX,QAAQ,CAACY,IAAI,CAACD,KAAK,CAAC;IACzB,OAAO,IAAI;EACf;EACAE,eAAeA,CAACd,IAAI,EAAE;IAClB,OAAO,IAAI,CAACE,UAAU,CAACF,IAAI,CAAC;IAC5B,OAAO,IAAI;EACf;EACAe,CAACA,CAACf,IAAI,EAAE;IACJ,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,OAAO,IAAI;EACf;EACAgB,CAACA,CAACJ,KAAK,EAAE;IACL,IAAI,CAACX,QAAQ,CAACY,IAAI,CAACD,KAAK,CAAC;IACzB,OAAO,IAAI;EACf;EACAK,CAACA,CAACjB,IAAI,EAAEW,KAAK,EAAE;IACX,IAAIA,KAAK,IAAI,IAAI,EAAE;MACf,IAAI,CAACT,UAAU,CAACF,IAAI,CAAC,GAAGW,KAAK;IACjC;IACA,OAAO,IAAI;EACf;EACAO,EAAEA,CAACC,KAAK,EAAEC,KAAK,EAAEf,QAAQ,GAAGe,KAAK,EAAE;IAC/B,IAAID,KAAK,CAACC,KAAK,CAAC,IAAI,IAAI,EAAE;MACtB,MAAMd,IAAI,GAAGP,OAAO,CAACI,EAAE,CAACiB,KAAK,EAAED,KAAK,CAACC,KAAK,CAAC,CAAC,CAACf,QAAQ,CAACA,QAAQ,CAAC;MAC/D,IAAI,CAACW,CAAC,CAACV,IAAI,CAAC;IAChB;EACJ;EACAe,CAACA,CAACF,KAAK,EAAEG,QAAQ,EAAEC,UAAU,EAAEC,aAAa,EAAE;IAC1C,IAAIL,KAAK,CAACG,QAAQ,CAAC,IAAI,IAAI,EAAE;MACzB,MAAMG,KAAK,GAAGD,aAAa,CAAC,CAAC;MAC7BC,KAAK,CAACC,GAAG,CAAEpB,IAAI,IAAK;QAChBA,IAAI,CAACD,QAAQ,CAACkB,UAAU,CAAC;QACzB,IAAI,CAACP,CAAC,CAACV,IAAI,CAAC;MAChB,CAAC,CAAC;IACN;EACJ;EACAqB,EAAEA,CAACR,KAAK,EAAEG,QAAQ,EAAEC,UAAU,EAAEC,aAAa,EAAE;IAC3C,IAAIL,KAAK,CAACG,QAAQ,CAAC,IAAI,IAAI,EAAE;MACzB,MAAMG,KAAK,GAAGD,aAAa,CAAC,CAAC;MAC7B,MAAMI,aAAa,GAAG,IAAI7B,OAAO,CAACwB,UAAU,CAAC;MAC7CE,KAAK,CAACC,GAAG,CAAEpB,IAAI,IAAK;QAChBsB,aAAa,CAACZ,CAAC,CAACV,IAAI,CAAC;MACzB,CAAC,CAAC;MACF,IAAI,CAACU,CAAC,CAACY,aAAa,CAAC;IACzB;EACJ;EACAC,QAAQA,CAAA,EAAG;IACP,MAAMC,WAAW,GAAGC,OAAO,CAAC,IAAI,CAAC9B,QAAQ,CAAC+B,MAAM,CAAC;IACjD,IAAIC,OAAO,GAAG,IAAI,IAAI,CAACjC,IAAI,EAAE;IAC7B,MAAME,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,KAAK,MAAMgC,aAAa,IAAIC,MAAM,CAACC,IAAI,CAAClC,UAAU,CAAC,EAAE;MACjD,MAAMmC,SAAS,GAAGnC,UAAU,CAACgC,aAAa,CAAC;MAC3C,IAAIG,SAAS,IAAI,IAAI,EAAE;QACnBJ,OAAO,IAAI,IAAIC,aAAa,KAAKrC,eAAe,CAAC,EAAE,GAAGwC,SAAS,CAAC,GAAG;MACvE;IACJ;IACA,OAAQJ,OAAO,IAAI,CAACH,WAAW,GAAG,IAAI,GAAG,IAAI,IAAI,CAAC7B,QAAQ,CAACyB,GAAG,CAAEV,CAAC,IAAKA,CAAC,CAACa,QAAQ,CAAC,CAAC,CAAC,CAACS,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,CAACtC,IAAI,GAAG;EACjH;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}