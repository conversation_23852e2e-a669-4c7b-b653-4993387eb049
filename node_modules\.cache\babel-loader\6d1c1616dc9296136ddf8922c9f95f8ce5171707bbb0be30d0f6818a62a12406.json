{"ast": null, "code": "import { escapeElement } from \"./escape-element\";\nexport class XmlText {\n  value;\n  constructor(value) {\n    this.value = value;\n  }\n  toString() {\n    return escapeElement(\"\" + this.value);\n  }\n}", "map": {"version": 3, "names": ["escapeElement", "XmlText", "value", "constructor", "toString"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@aws-sdk/xml-builder/dist-es/XmlText.js"], "sourcesContent": ["import { escapeElement } from \"./escape-element\";\nexport class XmlText {\n    value;\n    constructor(value) {\n        this.value = value;\n    }\n    toString() {\n        return escapeElement(\"\" + this.value);\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,kBAAkB;AAChD,OAAO,MAAMC,OAAO,CAAC;EACjBC,KAAK;EACLC,WAAWA,CAACD,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACAE,QAAQA,CAAA,EAAG;IACP,OAAOJ,aAAa,CAAC,EAAE,GAAG,IAAI,CAACE,KAAK,CAAC;EACzC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}