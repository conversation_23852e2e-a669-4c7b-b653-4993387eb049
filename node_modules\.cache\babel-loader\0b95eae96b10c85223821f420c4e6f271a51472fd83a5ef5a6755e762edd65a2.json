{"ast": null, "code": "import { TypeRegistry } from \"../TypeRegistry\";\nimport { Schema } from \"./Schema\";\nexport class MapSchema extends Schema {\n  constructor(name, traits, keySchema, valueSchema) {\n    super(name, traits);\n    this.name = name;\n    this.traits = traits;\n    this.keySchema = keySchema;\n    this.valueSchema = valueSchema;\n    this.symbol = MapSchema.symbol;\n  }\n  static [Symbol.hasInstance](lhs) {\n    const isPrototype = MapSchema.prototype.isPrototypeOf(lhs);\n    if (!isPrototype && typeof lhs === \"object\" && lhs !== null) {\n      const map = lhs;\n      return map.symbol === MapSchema.symbol;\n    }\n    return isPrototype;\n  }\n}\nMapSchema.symbol = Symbol.for(\"@smithy/core/schema::MapSchema\");\nexport function map(namespace, name, traits = {}, keySchema, valueSchema) {\n  const schema = new MapSchema(namespace + \"#\" + name, traits, keySchema, typeof valueSchema === \"function\" ? valueSchema() : valueSchema);\n  TypeRegistry.for(namespace).register(name, schema);\n  return schema;\n}", "map": {"version": 3, "names": ["TypeRegistry", "<PERSON><PERSON><PERSON>", "MapSchema", "constructor", "name", "traits", "keySchema", "valueSchema", "symbol", "Symbol", "hasInstance", "lhs", "isPrototype", "prototype", "isPrototypeOf", "map", "for", "namespace", "schema", "register"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/core/dist-es/submodules/schema/schemas/MapSchema.js"], "sourcesContent": ["import { TypeRegistry } from \"../TypeRegistry\";\nimport { Schema } from \"./Schema\";\nexport class MapSchema extends Schema {\n    constructor(name, traits, keySchema, valueSchema) {\n        super(name, traits);\n        this.name = name;\n        this.traits = traits;\n        this.keySchema = keySchema;\n        this.valueSchema = valueSchema;\n        this.symbol = MapSchema.symbol;\n    }\n    static [Symbol.hasInstance](lhs) {\n        const isPrototype = MapSchema.prototype.isPrototypeOf(lhs);\n        if (!isPrototype && typeof lhs === \"object\" && lhs !== null) {\n            const map = lhs;\n            return map.symbol === MapSchema.symbol;\n        }\n        return isPrototype;\n    }\n}\nMapSchema.symbol = Symbol.for(\"@smithy/core/schema::MapSchema\");\nexport function map(namespace, name, traits = {}, keySchema, valueSchema) {\n    const schema = new MapSchema(namespace + \"#\" + name, traits, keySchema, typeof valueSchema === \"function\" ? valueSchema() : valueSchema);\n    TypeRegistry.for(namespace).register(name, schema);\n    return schema;\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,MAAM,QAAQ,UAAU;AACjC,OAAO,MAAMC,SAAS,SAASD,MAAM,CAAC;EAClCE,WAAWA,CAACC,IAAI,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAE;IAC9C,KAAK,CAACH,IAAI,EAAEC,MAAM,CAAC;IACnB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,MAAM,GAAGN,SAAS,CAACM,MAAM;EAClC;EACA,QAAQC,MAAM,CAACC,WAAW,EAAEC,GAAG,EAAE;IAC7B,MAAMC,WAAW,GAAGV,SAAS,CAACW,SAAS,CAACC,aAAa,CAACH,GAAG,CAAC;IAC1D,IAAI,CAACC,WAAW,IAAI,OAAOD,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;MACzD,MAAMI,GAAG,GAAGJ,GAAG;MACf,OAAOI,GAAG,CAACP,MAAM,KAAKN,SAAS,CAACM,MAAM;IAC1C;IACA,OAAOI,WAAW;EACtB;AACJ;AACAV,SAAS,CAACM,MAAM,GAAGC,MAAM,CAACO,GAAG,CAAC,gCAAgC,CAAC;AAC/D,OAAO,SAASD,GAAGA,CAACE,SAAS,EAAEb,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAEC,SAAS,EAAEC,WAAW,EAAE;EACtE,MAAMW,MAAM,GAAG,IAAIhB,SAAS,CAACe,SAAS,GAAG,GAAG,GAAGb,IAAI,EAAEC,MAAM,EAAEC,SAAS,EAAE,OAAOC,WAAW,KAAK,UAAU,GAAGA,WAAW,CAAC,CAAC,GAAGA,WAAW,CAAC;EACxIP,YAAY,CAACgB,GAAG,CAACC,SAAS,CAAC,CAACE,QAAQ,CAACf,IAAI,EAAEc,MAAM,CAAC;EAClD,OAAOA,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}