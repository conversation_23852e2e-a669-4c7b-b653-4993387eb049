{"ast": null, "code": "export var HttpApiKeyAuthLocation;\n(function (HttpApiKeyAuthLocation) {\n  HttpApiKeyAuthLocation[\"HEADER\"] = \"header\";\n  HttpApiKeyAuthLocation[\"QUERY\"] = \"query\";\n})(HttpApiKeyAuthLocation || (HttpApiKeyAuthLocation = {}));", "map": {"version": 3, "names": ["HttpApiKeyAuthLocation"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/types/dist-es/auth/HttpApiKeyAuth.js"], "sourcesContent": ["export var HttpApiKeyAuthLocation;\n(function (HttpApiKeyAuthLocation) {\n    HttpApiKeyAuthLocation[\"HEADER\"] = \"header\";\n    HttpApiKeyAuthLocation[\"QUERY\"] = \"query\";\n})(HttpApiKeyAuthLocation || (HttpApiKeyAuthLocation = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,sBAAsB;AACjC,CAAC,UAAUA,sBAAsB,EAAE;EAC/BA,sBAAsB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC3CA,sBAAsB,CAAC,OAAO,CAAC,GAAG,OAAO;AAC7C,CAAC,EAAEA,sBAAsB,KAAKA,sBAAsB,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}