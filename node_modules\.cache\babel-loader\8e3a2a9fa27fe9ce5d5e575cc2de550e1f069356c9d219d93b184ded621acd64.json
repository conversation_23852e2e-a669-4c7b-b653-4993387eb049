{"ast": null, "code": "import { isValidHostLabel } from \"@smithy/util-endpoints\";\nimport { isIpAddress } from \"../isIpAddress\";\nexport const isVirtualHostableS3Bucket = (value, allowSubDomains = false) => {\n  if (allowSubDomains) {\n    for (const label of value.split(\".\")) {\n      if (!isVirtualHostableS3Bucket(label)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  if (!isValidHostLabel(value)) {\n    return false;\n  }\n  if (value.length < 3 || value.length > 63) {\n    return false;\n  }\n  if (value !== value.toLowerCase()) {\n    return false;\n  }\n  if (isIpAddress(value)) {\n    return false;\n  }\n  return true;\n};", "map": {"version": 3, "names": ["isValidHostLabel", "isIpAddress", "isVirtualHostableS3Bucket", "value", "allowSubDomains", "label", "split", "length", "toLowerCase"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/isVirtualHostableS3Bucket.js"], "sourcesContent": ["import { isValidHostLabel } from \"@smithy/util-endpoints\";\nimport { isIpAddress } from \"../isIpAddress\";\nexport const isVirtualHostableS3Bucket = (value, allowSubDomains = false) => {\n    if (allowSubDomains) {\n        for (const label of value.split(\".\")) {\n            if (!isVirtualHostableS3Bucket(label)) {\n                return false;\n            }\n        }\n        return true;\n    }\n    if (!isValidHostLabel(value)) {\n        return false;\n    }\n    if (value.length < 3 || value.length > 63) {\n        return false;\n    }\n    if (value !== value.toLowerCase()) {\n        return false;\n    }\n    if (isIpAddress(value)) {\n        return false;\n    }\n    return true;\n};\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAO,MAAMC,yBAAyB,GAAGA,CAACC,KAAK,EAAEC,eAAe,GAAG,KAAK,KAAK;EACzE,IAAIA,eAAe,EAAE;IACjB,KAAK,MAAMC,KAAK,IAAIF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,EAAE;MAClC,IAAI,CAACJ,yBAAyB,CAACG,KAAK,CAAC,EAAE;QACnC,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;EACA,IAAI,CAACL,gBAAgB,CAACG,KAAK,CAAC,EAAE;IAC1B,OAAO,KAAK;EAChB;EACA,IAAIA,KAAK,CAACI,MAAM,GAAG,CAAC,IAAIJ,KAAK,CAACI,MAAM,GAAG,EAAE,EAAE;IACvC,OAAO,KAAK;EAChB;EACA,IAAIJ,KAAK,KAAKA,KAAK,CAACK,WAAW,CAAC,CAAC,EAAE;IAC/B,OAAO,KAAK;EAChB;EACA,IAAIP,WAAW,CAACE,KAAK,CAAC,EAAE;IACpB,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}