{"ast": null, "code": "import { BedrockAgentRuntimeClient, InvokeAgentCommand } from '@aws-sdk/client-bedrock-agent-runtime';\nclass BedrockService {\n  constructor() {\n    this.client = null;\n    this.initializeClient();\n  }\n  initializeClient() {\n    try {\n      // Initialize AWS Bedrock Agent Runtime client\n      this.client = new BedrockAgentRuntimeClient({\n        region: process.env.REACT_APP_AWS_REGION || 'us-east-1',\n        credentials: {\n          accessKeyId: process.env.REACT_APP_AWS_ACCESS_KEY_ID || 'your-access-key',\n          secretAccessKey: process.env.REACT_APP_AWS_SECRET_ACCESS_KEY || 'your-secret-key'\n        }\n      });\n    } catch (error) {\n      console.error('Failed to initialize Bedrock client:', error);\n    }\n  }\n  async sendMessage(message, onChunk = null) {\n    if (!this.client) {\n      throw new Error('Bedrock client not initialized');\n    }\n    const agentId = process.env.REACT_APP_BEDROCK_AGENT_ID;\n    const agentAliasId = process.env.REACT_APP_BEDROCK_AGENT_ALIAS_ID;\n    if (!agentId || !agentAliasId) {\n      throw new Error('Agent ID and Agent Alias ID must be configured in environment variables');\n    }\n    try {\n      const command = new InvokeAgentCommand({\n        agentId: agentId,\n        agentAliasId: agentAliasId,\n        sessionId: this.generateSessionId(),\n        inputText: message\n      });\n      const response = await this.client.send(command);\n\n      // Handle streaming response\n      let fullResponse = '';\n      let chartData = null;\n      if (response.completion) {\n        for await (const chunk of response.completion) {\n          if (chunk.chunk && chunk.chunk.bytes) {\n            const chunkText = new TextDecoder().decode(chunk.chunk.bytes);\n            fullResponse += chunkText;\n\n            // Call onChunk callback if provided for real-time updates\n            if (onChunk) {\n              onChunk(chunkText);\n            }\n          }\n        }\n      }\n\n      // Try to parse response for chart data\n      try {\n        const parsedResponse = JSON.parse(fullResponse);\n        if (parsedResponse.type === 'chart') {\n          chartData = parsedResponse;\n          fullResponse = parsedResponse.text || '';\n        }\n      } catch (parseError) {\n        // Response is not JSON, treat as regular text\n        // Check if response contains chart data markers\n        const chartMatch = fullResponse.match(/\\{[\\s\\S]*\"type\":\\s*\"chart\"[\\s\\S]*\\}/);\n        if (chartMatch) {\n          try {\n            chartData = JSON.parse(chartMatch[0]);\n            fullResponse = fullResponse.replace(chartMatch[0], '').trim();\n          } catch (e) {\n            // Failed to parse chart data, keep as text\n          }\n        }\n      }\n      return {\n        text: fullResponse,\n        chartData: chartData,\n        timestamp: new Date().toISOString()\n      };\n    } catch (error) {\n      console.error('Error calling Bedrock agent:', error);\n      throw new Error(`Failed to get response from Bedrock agent: ${error.message}`);\n    }\n  }\n  generateSessionId() {\n    // Generate a unique session ID for the conversation\n    return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  // Mock method for development/testing\n  async mockSendMessage(message) {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // Mock responses based on message content\n    if (message.toLowerCase().includes('chart') || message.toLowerCase().includes('pie')) {\n      return {\n        text: \"Here's the supplier performance data you requested:\",\n        chartData: {\n          type: \"chart\",\n          chartType: \"pie\",\n          title: \"Supplier Performance Scores\",\n          data: [{\n            label: \"Supplier A\",\n            value: 85\n          }, {\n            label: \"Supplier B\",\n            value: 92\n          }, {\n            label: \"Supplier C\",\n            value: 78\n          }, {\n            label: \"Supplier D\",\n            value: 88\n          }, {\n            label: \"Supplier E\",\n            value: 95\n          }]\n        },\n        timestamp: new Date().toISOString()\n      };\n    } else if (message.toLowerCase().includes('bar')) {\n      return {\n        text: \"Monthly delivery performance:\",\n        chartData: {\n          type: \"chart\",\n          chartType: \"bar\",\n          title: \"Monthly Deliveries\",\n          data: [{\n            label: \"Jan\",\n            value: 120\n          }, {\n            label: \"Feb\",\n            value: 135\n          }, {\n            label: \"Mar\",\n            value: 98\n          }, {\n            label: \"Apr\",\n            value: 156\n          }, {\n            label: \"May\",\n            value: 142\n          }]\n        },\n        timestamp: new Date().toISOString()\n      };\n    } else {\n      return {\n        text: `I understand you're asking about: \"${message}\". I'm here to help with supplier-related queries. You can ask me to create charts, analyze supplier performance, or get delivery information.`,\n        chartData: null,\n        timestamp: new Date().toISOString()\n      };\n    }\n  }\n}\nexport default new BedrockService();", "map": {"version": 3, "names": ["BedrockAgentRuntimeClient", "InvokeAgentCommand", "BedrockService", "constructor", "client", "initializeClient", "region", "process", "env", "REACT_APP_AWS_REGION", "credentials", "accessKeyId", "REACT_APP_AWS_ACCESS_KEY_ID", "secretAccessKey", "REACT_APP_AWS_SECRET_ACCESS_KEY", "error", "console", "sendMessage", "message", "onChunk", "Error", "agentId", "REACT_APP_BEDROCK_AGENT_ID", "agentAliasId", "REACT_APP_BEDROCK_AGENT_ALIAS_ID", "command", "sessionId", "generateSessionId", "inputText", "response", "send", "fullResponse", "chartData", "completion", "chunk", "bytes", "chunkText", "TextDecoder", "decode", "parsedResponse", "JSON", "parse", "type", "text", "parseError", "chartMatch", "match", "replace", "trim", "e", "timestamp", "Date", "toISOString", "now", "Math", "random", "toString", "substr", "mockSendMessage", "Promise", "resolve", "setTimeout", "toLowerCase", "includes", "chartType", "title", "data", "label", "value"], "sources": ["C:/xampp/htdocs/aiUi/src/services/bedrockService.js"], "sourcesContent": ["import { BedrockAgentRuntimeClient, InvokeAgentCommand } from '@aws-sdk/client-bedrock-agent-runtime';\n\nclass BedrockService {\n  constructor() {\n    this.client = null;\n    this.initializeClient();\n  }\n\n  initializeClient() {\n    try {\n      // Initialize AWS Bedrock Agent Runtime client\n      this.client = new BedrockAgentRuntimeClient({\n        region: process.env.REACT_APP_AWS_REGION || 'us-east-1',\n        credentials: {\n          accessKeyId: process.env.REACT_APP_AWS_ACCESS_KEY_ID || 'your-access-key',\n          secretAccessKey: process.env.REACT_APP_AWS_SECRET_ACCESS_KEY || 'your-secret-key',\n        \n        }\n      });\n    } catch (error) {\n      console.error('Failed to initialize Bedrock client:', error);\n    }\n  }\n\n  async sendMessage(message, onChunk = null) {\n    if (!this.client) {\n      throw new Error('Bedrock client not initialized');\n    }\n\n    const agentId = process.env.REACT_APP_BEDROCK_AGENT_ID;\n    const agentAliasId = process.env.REACT_APP_BEDROCK_AGENT_ALIAS_ID;\n\n    if (!agentId || !agentAliasId) {\n      throw new Error('Agent ID and Agent Alias ID must be configured in environment variables');\n    }\n\n    try {\n      const command = new InvokeAgentCommand({\n        agentId: agentId,\n        agentAliasId: agentAliasId,\n        sessionId: this.generateSessionId(),\n        inputText: message,\n      });\n\n      const response = await this.client.send(command);\n      \n      // Handle streaming response\n      let fullResponse = '';\n      let chartData = null;\n      \n      if (response.completion) {\n        for await (const chunk of response.completion) {\n          if (chunk.chunk && chunk.chunk.bytes) {\n            const chunkText = new TextDecoder().decode(chunk.chunk.bytes);\n            fullResponse += chunkText;\n            \n            // Call onChunk callback if provided for real-time updates\n            if (onChunk) {\n              onChunk(chunkText);\n            }\n          }\n        }\n      }\n\n      // Try to parse response for chart data\n      try {\n        const parsedResponse = JSON.parse(fullResponse);\n        if (parsedResponse.type === 'chart') {\n          chartData = parsedResponse;\n          fullResponse = parsedResponse.text || '';\n        }\n      } catch (parseError) {\n        // Response is not JSON, treat as regular text\n        // Check if response contains chart data markers\n        const chartMatch = fullResponse.match(/\\{[\\s\\S]*\"type\":\\s*\"chart\"[\\s\\S]*\\}/);\n        if (chartMatch) {\n          try {\n            chartData = JSON.parse(chartMatch[0]);\n            fullResponse = fullResponse.replace(chartMatch[0], '').trim();\n          } catch (e) {\n            // Failed to parse chart data, keep as text\n          }\n        }\n      }\n\n      return {\n        text: fullResponse,\n        chartData: chartData,\n        timestamp: new Date().toISOString()\n      };\n\n    } catch (error) {\n      console.error('Error calling Bedrock agent:', error);\n      throw new Error(`Failed to get response from Bedrock agent: ${error.message}`);\n    }\n  }\n\n  generateSessionId() {\n    // Generate a unique session ID for the conversation\n    return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  // Mock method for development/testing\n  async mockSendMessage(message) {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // Mock responses based on message content\n    if (message.toLowerCase().includes('chart') || message.toLowerCase().includes('pie')) {\n      return {\n        text: \"Here's the supplier performance data you requested:\",\n        chartData: {\n          type: \"chart\",\n          chartType: \"pie\",\n          title: \"Supplier Performance Scores\",\n          data: [\n            { label: \"Supplier A\", value: 85 },\n            { label: \"Supplier B\", value: 92 },\n            { label: \"Supplier C\", value: 78 },\n            { label: \"Supplier D\", value: 88 },\n            { label: \"Supplier E\", value: 95 }\n          ]\n        },\n        timestamp: new Date().toISOString()\n      };\n    } else if (message.toLowerCase().includes('bar')) {\n      return {\n        text: \"Monthly delivery performance:\",\n        chartData: {\n          type: \"chart\",\n          chartType: \"bar\",\n          title: \"Monthly Deliveries\",\n          data: [\n            { label: \"Jan\", value: 120 },\n            { label: \"Feb\", value: 135 },\n            { label: \"Mar\", value: 98 },\n            { label: \"Apr\", value: 156 },\n            { label: \"May\", value: 142 }\n          ]\n        },\n        timestamp: new Date().toISOString()\n      };\n    } else {\n      return {\n        text: `I understand you're asking about: \"${message}\". I'm here to help with supplier-related queries. You can ask me to create charts, analyze supplier performance, or get delivery information.`,\n        chartData: null,\n        timestamp: new Date().toISOString()\n      };\n    }\n  }\n}\n\nexport default new BedrockService();\n"], "mappings": "AAAA,SAASA,yBAAyB,EAAEC,kBAAkB,QAAQ,uCAAuC;AAErG,MAAMC,cAAc,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,gBAAgB,CAAC,CAAC;EACzB;EAEAA,gBAAgBA,CAAA,EAAG;IACjB,IAAI;MACF;MACA,IAAI,CAACD,MAAM,GAAG,IAAIJ,yBAAyB,CAAC;QAC1CM,MAAM,EAAEC,OAAO,CAACC,GAAG,CAACC,oBAAoB,IAAI,WAAW;QACvDC,WAAW,EAAE;UACXC,WAAW,EAAEJ,OAAO,CAACC,GAAG,CAACI,2BAA2B,IAAI,iBAAiB;UACzEC,eAAe,EAAEN,OAAO,CAACC,GAAG,CAACM,+BAA+B,IAAI;QAElE;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D;EACF;EAEA,MAAME,WAAWA,CAACC,OAAO,EAAEC,OAAO,GAAG,IAAI,EAAE;IACzC,IAAI,CAAC,IAAI,CAACf,MAAM,EAAE;MAChB,MAAM,IAAIgB,KAAK,CAAC,gCAAgC,CAAC;IACnD;IAEA,MAAMC,OAAO,GAAGd,OAAO,CAACC,GAAG,CAACc,0BAA0B;IACtD,MAAMC,YAAY,GAAGhB,OAAO,CAACC,GAAG,CAACgB,gCAAgC;IAEjE,IAAI,CAACH,OAAO,IAAI,CAACE,YAAY,EAAE;MAC7B,MAAM,IAAIH,KAAK,CAAC,yEAAyE,CAAC;IAC5F;IAEA,IAAI;MACF,MAAMK,OAAO,GAAG,IAAIxB,kBAAkB,CAAC;QACrCoB,OAAO,EAAEA,OAAO;QAChBE,YAAY,EAAEA,YAAY;QAC1BG,SAAS,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC;QACnCC,SAAS,EAAEV;MACb,CAAC,CAAC;MAEF,MAAMW,QAAQ,GAAG,MAAM,IAAI,CAACzB,MAAM,CAAC0B,IAAI,CAACL,OAAO,CAAC;;MAEhD;MACA,IAAIM,YAAY,GAAG,EAAE;MACrB,IAAIC,SAAS,GAAG,IAAI;MAEpB,IAAIH,QAAQ,CAACI,UAAU,EAAE;QACvB,WAAW,MAAMC,KAAK,IAAIL,QAAQ,CAACI,UAAU,EAAE;UAC7C,IAAIC,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACC,KAAK,EAAE;YACpC,MAAMC,SAAS,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACJ,KAAK,CAACA,KAAK,CAACC,KAAK,CAAC;YAC7DJ,YAAY,IAAIK,SAAS;;YAEzB;YACA,IAAIjB,OAAO,EAAE;cACXA,OAAO,CAACiB,SAAS,CAAC;YACpB;UACF;QACF;MACF;;MAEA;MACA,IAAI;QACF,MAAMG,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACV,YAAY,CAAC;QAC/C,IAAIQ,cAAc,CAACG,IAAI,KAAK,OAAO,EAAE;UACnCV,SAAS,GAAGO,cAAc;UAC1BR,YAAY,GAAGQ,cAAc,CAACI,IAAI,IAAI,EAAE;QAC1C;MACF,CAAC,CAAC,OAAOC,UAAU,EAAE;QACnB;QACA;QACA,MAAMC,UAAU,GAAGd,YAAY,CAACe,KAAK,CAAC,qCAAqC,CAAC;QAC5E,IAAID,UAAU,EAAE;UACd,IAAI;YACFb,SAAS,GAAGQ,IAAI,CAACC,KAAK,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC;YACrCd,YAAY,GAAGA,YAAY,CAACgB,OAAO,CAACF,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC;UAC/D,CAAC,CAAC,OAAOC,CAAC,EAAE;YACV;UAAA;QAEJ;MACF;MAEA,OAAO;QACLN,IAAI,EAAEZ,YAAY;QAClBC,SAAS,EAAEA,SAAS;QACpBkB,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;IAEH,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAM,IAAIK,KAAK,CAAC,8CAA8CL,KAAK,CAACG,OAAO,EAAE,CAAC;IAChF;EACF;EAEAS,iBAAiBA,CAAA,EAAG;IAClB;IACA,OAAO,WAAWwB,IAAI,CAACE,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EAC3E;;EAEA;EACA,MAAMC,eAAeA,CAACxC,OAAO,EAAE;IAC7B;IACA,MAAM,IAAIyC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;IAEvD;IACA,IAAI1C,OAAO,CAAC4C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAI7C,OAAO,CAAC4C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;MACpF,OAAO;QACLpB,IAAI,EAAE,qDAAqD;QAC3DX,SAAS,EAAE;UACTU,IAAI,EAAE,OAAO;UACbsB,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAE,6BAA6B;UACpCC,IAAI,EAAE,CACJ;YAAEC,KAAK,EAAE,YAAY;YAAEC,KAAK,EAAE;UAAG,CAAC,EAClC;YAAED,KAAK,EAAE,YAAY;YAAEC,KAAK,EAAE;UAAG,CAAC,EAClC;YAAED,KAAK,EAAE,YAAY;YAAEC,KAAK,EAAE;UAAG,CAAC,EAClC;YAAED,KAAK,EAAE,YAAY;YAAEC,KAAK,EAAE;UAAG,CAAC,EAClC;YAAED,KAAK,EAAE,YAAY;YAAEC,KAAK,EAAE;UAAG,CAAC;QAEtC,CAAC;QACDlB,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;IACH,CAAC,MAAM,IAAIlC,OAAO,CAAC4C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;MAChD,OAAO;QACLpB,IAAI,EAAE,+BAA+B;QACrCX,SAAS,EAAE;UACTU,IAAI,EAAE,OAAO;UACbsB,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAE,oBAAoB;UAC3BC,IAAI,EAAE,CACJ;YAAEC,KAAK,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAI,CAAC,EAC5B;YAAED,KAAK,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAI,CAAC,EAC5B;YAAED,KAAK,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAG,CAAC,EAC3B;YAAED,KAAK,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAI,CAAC,EAC5B;YAAED,KAAK,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAI,CAAC;QAEhC,CAAC;QACDlB,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLT,IAAI,EAAE,sCAAsCzB,OAAO,gJAAgJ;QACnMc,SAAS,EAAE,IAAI;QACfkB,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;IACH;EACF;AACF;AAEA,eAAe,IAAIlD,cAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}