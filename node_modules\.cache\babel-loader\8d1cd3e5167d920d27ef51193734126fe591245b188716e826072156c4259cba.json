{"ast": null, "code": "const ReadableStreamRef = typeof ReadableStream === \"function\" ? ReadableStream : function () {};\nexport class ChecksumStream extends ReadableStreamRef {}", "map": {"version": 3, "names": ["ReadableStreamRef", "ReadableStream", "ChecksumStream"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/util-stream/dist-es/checksum/ChecksumStream.browser.js"], "sourcesContent": ["const ReadableStreamRef = typeof ReadableStream === \"function\" ? ReadableStream : function () { };\nexport class ChecksumStream extends ReadableStreamRef {\n}\n"], "mappings": "AAAA,MAAMA,iBAAiB,GAAG,OAAOC,cAAc,KAAK,UAAU,GAAGA,cAAc,GAAG,YAAY,CAAE,CAAC;AACjG,OAAO,MAAMC,cAAc,SAASF,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}