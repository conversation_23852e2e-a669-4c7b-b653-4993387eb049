{"ast": null, "code": "export const SCHEMA = {\n  BLOB: 21,\n  STREAMING_BLOB: 42,\n  B<PERSON><PERSON><PERSON><PERSON>: 2,\n  STRING: 0,\n  NUMERIC: 1,\n  BIG_INTEGER: 17,\n  BIG_DECIMAL: 19,\n  DOCUMENT: 15,\n  TIMESTAMP_DEFAULT: 4,\n  TIMESTAMP_DATE_TIME: 5,\n  TIMESTAMP_HTTP_DATE: 6,\n  TIMESTAMP_EPOCH_SECONDS: 7,\n  LIST_MODIFIER: 64,\n  MAP_MODIFIER: 128\n};", "map": {"version": 3, "names": ["SCHEMA", "BLOB", "STREAMING_BLOB", "BOOLEAN", "STRING", "NUMERIC", "BIG_INTEGER", "BIG_DECIMAL", "DOCUMENT", "TIMESTAMP_DEFAULT", "TIMESTAMP_DATE_TIME", "TIMESTAMP_HTTP_DATE", "TIMESTAMP_EPOCH_SECONDS", "LIST_MODIFIER", "MAP_MODIFIER"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/core/dist-es/submodules/schema/schemas/sentinels.js"], "sourcesContent": ["export const SCHEMA = {\n    BLOB: 21,\n    STREAMING_BLOB: 42,\n    B<PERSON><PERSON><PERSON><PERSON>: 2,\n    STRING: 0,\n    NUMERIC: 1,\n    BIG_INTEGER: 17,\n    BIG_DECIMAL: 19,\n    DOCUMENT: 15,\n    TIMESTAMP_DEFAULT: 4,\n    TIMESTAMP_DATE_TIME: 5,\n    TIMESTAMP_HTTP_DATE: 6,\n    TIMESTAMP_EPOCH_SECONDS: 7,\n    LIST_MODIFIER: 64,\n    MAP_MODIFIER: 128,\n};\n"], "mappings": "AAAA,OAAO,MAAMA,MAAM,GAAG;EAClBC,IAAI,EAAE,EAAE;EACRC,cAAc,EAAE,EAAE;EAClBC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,CAAC;EACVC,WAAW,EAAE,EAAE;EACfC,WAAW,EAAE,EAAE;EACfC,QAAQ,EAAE,EAAE;EACZC,iBAAiB,EAAE,CAAC;EACpBC,mBAAmB,EAAE,CAAC;EACtBC,mBAAmB,EAAE,CAAC;EACtBC,uBAAuB,EAAE,CAAC;EAC1BC,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}