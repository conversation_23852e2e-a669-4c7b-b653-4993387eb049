{"ast": null, "code": "export async function splitStream(stream) {\n  if (typeof stream.stream === \"function\") {\n    stream = stream.stream();\n  }\n  const readableStream = stream;\n  return readableStream.tee();\n}", "map": {"version": 3, "names": ["splitStream", "stream", "readableStream", "tee"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/util-stream/dist-es/splitStream.browser.js"], "sourcesContent": ["export async function splitStream(stream) {\n    if (typeof stream.stream === \"function\") {\n        stream = stream.stream();\n    }\n    const readableStream = stream;\n    return readableStream.tee();\n}\n"], "mappings": "AAAA,OAAO,eAAeA,WAAWA,CAACC,MAAM,EAAE;EACtC,IAAI,OAAOA,MAAM,CAACA,MAAM,KAAK,UAAU,EAAE;IACrCA,MAAM,GAAGA,MAAM,CAACA,MAAM,CAAC,CAAC;EAC5B;EACA,MAAMC,cAAc,GAAGD,MAAM;EAC7B,OAAOC,cAAc,CAACC,GAAG,CAAC,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}