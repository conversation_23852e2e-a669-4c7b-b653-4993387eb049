[{"C:\\xampp\\htdocs\\aiUi\\src\\index.js": "1", "C:\\xampp\\htdocs\\aiUi\\src\\App.js": "2", "C:\\xampp\\htdocs\\aiUi\\src\\components\\ChatWindow.js": "3", "C:\\xampp\\htdocs\\aiUi\\src\\services\\bedrockService.js": "4", "C:\\xampp\\htdocs\\aiUi\\src\\components\\ChatBubble.js": "5", "C:\\xampp\\htdocs\\aiUi\\src\\components\\ChartRenderer.js": "6"}, {"size": 254, "mtime": 1753833675627, "results": "7", "hashOfConfig": "8"}, {"size": 6849, "mtime": 1753833804091, "results": "9", "hashOfConfig": "8"}, {"size": 2651, "mtime": 1753833775135, "results": "10", "hashOfConfig": "8"}, {"size": 4967, "mtime": 1753835975226, "results": "11", "hashOfConfig": "8"}, {"size": 1256, "mtime": 1753833761384, "results": "12", "hashOfConfig": "8"}, {"size": 2935, "mtime": 1753833748169, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vwnfdi", {"filePath": "17", "messages": "18", "suppressedMessages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\xampp\\htdocs\\aiUi\\src\\index.js", [], [], "C:\\xampp\\htdocs\\aiUi\\src\\App.js", [], [], "C:\\xampp\\htdocs\\aiUi\\src\\components\\ChatWindow.js", [], [], "C:\\xampp\\htdocs\\aiUi\\src\\services\\bedrockService.js", ["32"], [], "C:\\xampp\\htdocs\\aiUi\\src\\components\\ChatBubble.js", [], [], "C:\\xampp\\htdocs\\aiUi\\src\\components\\ChartRenderer.js", [], [], {"ruleId": "33", "severity": 1, "message": "34", "line": 154, "column": 1, "nodeType": "35", "endLine": 154, "endColumn": 37}, "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration"]