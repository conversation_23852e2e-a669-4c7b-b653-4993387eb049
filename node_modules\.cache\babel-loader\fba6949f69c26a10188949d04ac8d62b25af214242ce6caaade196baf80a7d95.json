{"ast": null, "code": "import { debugId, toDebugString } from \"./debug\";\nimport { EndpointError } from \"./types\";\nimport { evaluateRules } from \"./utils\";\nexport const resolveEndpoint = (ruleSetObject, options) => {\n  const {\n    endpointParams,\n    logger\n  } = options;\n  const {\n    parameters,\n    rules\n  } = ruleSetObject;\n  options.logger?.debug?.(`${debugId} Initial EndpointParams: ${toDebugString(endpointParams)}`);\n  const paramsWithDefault = Object.entries(parameters).filter(([, v]) => v.default != null).map(([k, v]) => [k, v.default]);\n  if (paramsWithDefault.length > 0) {\n    for (const [paramKey, paramDefaultValue] of paramsWithDefault) {\n      endpointParams[paramKey] = endpointParams[paramKey] ?? paramDefaultValue;\n    }\n  }\n  const requiredParams = Object.entries(parameters).filter(([, v]) => v.required).map(([k]) => k);\n  for (const requiredParam of requiredParams) {\n    if (endpointParams[requiredParam] == null) {\n      throw new EndpointError(`Missing required parameter: '${requiredParam}'`);\n    }\n  }\n  const endpoint = evaluateRules(rules, {\n    endpointParams,\n    logger,\n    referenceRecord: {}\n  });\n  options.logger?.debug?.(`${debugId} Resolved endpoint: ${toDebugString(endpoint)}`);\n  return endpoint;\n};", "map": {"version": 3, "names": ["debugId", "toDebugString", "EndpointError", "evaluateRules", "resolveEndpoint", "ruleSetObject", "options", "endpointParams", "logger", "parameters", "rules", "debug", "paramsWithDefault", "Object", "entries", "filter", "v", "default", "map", "k", "length", "<PERSON><PERSON><PERSON><PERSON>", "paramDefaultValue", "requiredParams", "required", "required<PERSON><PERSON><PERSON>", "endpoint", "referenceRecord"], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/util-endpoints/dist-es/resolveEndpoint.js"], "sourcesContent": ["import { debugId, toDebugString } from \"./debug\";\nimport { EndpointError } from \"./types\";\nimport { evaluateRules } from \"./utils\";\nexport const resolveEndpoint = (ruleSetObject, options) => {\n    const { endpointParams, logger } = options;\n    const { parameters, rules } = ruleSetObject;\n    options.logger?.debug?.(`${debugId} Initial EndpointParams: ${toDebugString(endpointParams)}`);\n    const paramsWithDefault = Object.entries(parameters)\n        .filter(([, v]) => v.default != null)\n        .map(([k, v]) => [k, v.default]);\n    if (paramsWithDefault.length > 0) {\n        for (const [paramKey, paramDefaultValue] of paramsWithDefault) {\n            endpointParams[paramKey] = endpointParams[paramKey] ?? paramDefaultValue;\n        }\n    }\n    const requiredParams = Object.entries(parameters)\n        .filter(([, v]) => v.required)\n        .map(([k]) => k);\n    for (const requiredParam of requiredParams) {\n        if (endpointParams[requiredParam] == null) {\n            throw new EndpointError(`Missing required parameter: '${requiredParam}'`);\n        }\n    }\n    const endpoint = evaluateRules(rules, { endpointParams, logger, referenceRecord: {} });\n    options.logger?.debug?.(`${debugId} Resolved endpoint: ${toDebugString(endpoint)}`);\n    return endpoint;\n};\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,aAAa,QAAQ,SAAS;AAChD,SAASC,aAAa,QAAQ,SAAS;AACvC,SAASC,aAAa,QAAQ,SAAS;AACvC,OAAO,MAAMC,eAAe,GAAGA,CAACC,aAAa,EAAEC,OAAO,KAAK;EACvD,MAAM;IAAEC,cAAc;IAAEC;EAAO,CAAC,GAAGF,OAAO;EAC1C,MAAM;IAAEG,UAAU;IAAEC;EAAM,CAAC,GAAGL,aAAa;EAC3CC,OAAO,CAACE,MAAM,EAAEG,KAAK,GAAG,GAAGX,OAAO,4BAA4BC,aAAa,CAACM,cAAc,CAAC,EAAE,CAAC;EAC9F,MAAMK,iBAAiB,GAAGC,MAAM,CAACC,OAAO,CAACL,UAAU,CAAC,CAC/CM,MAAM,CAAC,CAAC,GAAGC,CAAC,CAAC,KAAKA,CAAC,CAACC,OAAO,IAAI,IAAI,CAAC,CACpCC,GAAG,CAAC,CAAC,CAACC,CAAC,EAAEH,CAAC,CAAC,KAAK,CAACG,CAAC,EAAEH,CAAC,CAACC,OAAO,CAAC,CAAC;EACpC,IAAIL,iBAAiB,CAACQ,MAAM,GAAG,CAAC,EAAE;IAC9B,KAAK,MAAM,CAACC,QAAQ,EAAEC,iBAAiB,CAAC,IAAIV,iBAAiB,EAAE;MAC3DL,cAAc,CAACc,QAAQ,CAAC,GAAGd,cAAc,CAACc,QAAQ,CAAC,IAAIC,iBAAiB;IAC5E;EACJ;EACA,MAAMC,cAAc,GAAGV,MAAM,CAACC,OAAO,CAACL,UAAU,CAAC,CAC5CM,MAAM,CAAC,CAAC,GAAGC,CAAC,CAAC,KAAKA,CAAC,CAACQ,QAAQ,CAAC,CAC7BN,GAAG,CAAC,CAAC,CAACC,CAAC,CAAC,KAAKA,CAAC,CAAC;EACpB,KAAK,MAAMM,aAAa,IAAIF,cAAc,EAAE;IACxC,IAAIhB,cAAc,CAACkB,aAAa,CAAC,IAAI,IAAI,EAAE;MACvC,MAAM,IAAIvB,aAAa,CAAC,gCAAgCuB,aAAa,GAAG,CAAC;IAC7E;EACJ;EACA,MAAMC,QAAQ,GAAGvB,aAAa,CAACO,KAAK,EAAE;IAAEH,cAAc;IAAEC,MAAM;IAAEmB,eAAe,EAAE,CAAC;EAAE,CAAC,CAAC;EACtFrB,OAAO,CAACE,MAAM,EAAEG,KAAK,GAAG,GAAGX,OAAO,uBAAuBC,aAAa,CAACyB,QAAQ,CAAC,EAAE,CAAC;EACnF,OAAOA,QAAQ;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}