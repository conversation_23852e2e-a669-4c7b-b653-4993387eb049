{"ast": null, "code": "export * from \"./customEndpointFunctions\";\nexport * from \"./evaluateRules\";", "map": {"version": 3, "names": [], "sources": ["C:/xampp/htdocs/aiUi/node_modules/@smithy/util-endpoints/dist-es/utils/index.js"], "sourcesContent": ["export * from \"./customEndpointFunctions\";\nexport * from \"./evaluateRules\";\n"], "mappings": "AAAA,cAAc,2BAA2B;AACzC,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}